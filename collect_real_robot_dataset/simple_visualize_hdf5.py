#!/usr/bin/env python3
"""
简化版HDF5数据可视化脚本
避免NumPy版本兼容性问题，使用基础库

Usage:
    python simple_visualize_hdf5.py --file /path/to/file.hdf5
    python simple_visualize_hdf5.py --data_dir /home/<USER>/act-plus-plus/datasets/ym_touch
"""

import os
import argparse
import h5py
import cv2

try:
    import numpy as np
    NUMPY_AVAILABLE = True
except ImportError:
    print("Warning: NumPy not available, some features will be limited")
    NUMPY_AVAILABLE = False

class SimpleHDF5Visualizer:
    def __init__(self, hdf5_path):
        self.hdf5_path = hdf5_path
        self.current_frame = 0
        self.total_frames = 0
        
        # 加载数据信息
        self.load_data_info()
        
    def load_data_info(self):
        """加载HDF5数据信息"""
        print(f"Loading data from {self.hdf5_path}")
        
        with h5py.File(self.hdf5_path, 'r') as f:
            # 打印文件结构
            print("\nHDF5 file structure:")
            self.print_structure(f)
            
            # 获取数据形状信息
            self.data_info = {}
            
            # 图像数据信息
            if 'observations/images' in f:
                img_group = f['observations/images']
                self.data_info['images'] = {}
                for cam_name in img_group.keys():
                    shape = img_group[cam_name].shape
                    self.data_info['images'][cam_name] = shape
                    print(f"Found {cam_name} images: {shape}")
                    if self.total_frames == 0:
                        self.total_frames = shape[0]
            
            # 关节数据信息
            if 'observations/qpos' in f:
                shape = f['observations/qpos'].shape
                self.data_info['qpos'] = shape
                print(f"Found qpos: {shape}")
                if self.total_frames == 0:
                    self.total_frames = shape[0]
            
            # 动作数据信息
            if 'action' in f:
                shape = f['action'].shape
                self.data_info['action'] = shape
                print(f"Found action: {shape}")
                if self.total_frames == 0:
                    self.total_frames = shape[0]
        
        print(f"\nTotal frames: {self.total_frames}")
    
    def print_structure(self, group, indent=""):
        """递归打印HDF5文件结构"""
        for key in group.keys():
            item = group[key]
            if isinstance(item, h5py.Dataset):
                print(f"{indent}{key}: {item.shape} {item.dtype}")
            elif isinstance(item, h5py.Group):
                print(f"{indent}{key}/")
                self.print_structure(item, indent + "  ")
    
    def show_frame(self, frame_idx):
        """显示指定帧的数据"""
        if frame_idx >= self.total_frames:
            print(f"Frame {frame_idx} exceeds total frames {self.total_frames}")
            return
        
        print(f"\n{'='*60}")
        print(f"Frame {frame_idx} / {self.total_frames-1}")
        print(f"{'='*60}")
        
        with h5py.File(self.hdf5_path, 'r') as f:
            # 显示图像
            if 'observations/images' in f:
                img_group = f['observations/images']
                for cam_name in img_group.keys():
                    img_data = img_group[cam_name][frame_idx]
                    print(f"\n{cam_name.title()} Camera:")
                    print(f"  Shape: {img_data.shape}")
                    print(f"  Data type: {img_data.dtype}")
                    print(f"  Min: {img_data.min()}, Max: {img_data.max()}")
                    
                    # 保存图像到文件
                    if len(img_data.shape) == 3:
                        # 转换BGR到RGB
                        img_rgb = cv2.cvtColor(img_data, cv2.COLOR_BGR2RGB)
                        output_path = f"frame_{frame_idx}_{cam_name}.jpg"
                        cv2.imwrite(output_path, img_data)  # 保存为BGR格式
                        print(f"  Saved to: {output_path}")
            
            # 显示关节数据
            if 'observations/qpos' in f:
                qpos_data = f['observations/qpos'][frame_idx]
                print(f"\nJoint Positions:")
                print(f"  Shape: {qpos_data.shape}")
                print(f"  Values: {qpos_data}")
                if NUMPY_AVAILABLE:
                    print(f"  Min: {np.min(qpos_data):.4f}, Max: {np.max(qpos_data):.4f}")
                    print(f"  Mean: {np.mean(qpos_data):.4f}, Std: {np.std(qpos_data):.4f}")
            
            # 显示动作数据
            if 'action' in f:
                action_data = f['action'][frame_idx]
                print(f"\nActions:")
                print(f"  Shape: {action_data.shape}")
                print(f"  Values: {action_data}")
                if NUMPY_AVAILABLE:
                    print(f"  Min: {np.min(action_data):.4f}, Max: {np.max(action_data):.4f}")
                    print(f"  Mean: {np.mean(action_data):.4f}, Std: {np.std(action_data):.4f}")
    
    def interactive_view(self):
        """交互式查看数据"""
        print(f"\n{'='*60}")
        print("Interactive HDF5 Data Viewer")
        print(f"{'='*60}")
        print("Commands:")
        print("  <number>  - Go to specific frame")
        print("  n/next    - Next frame")
        print("  p/prev    - Previous frame")
        print("  f/first   - Go to first frame")
        print("  l/last    - Go to last frame")
        print("  s/stats   - Show dataset statistics")
        print("  q/quit    - Quit")
        print(f"{'='*60}")
        
        while True:
            try:
                # 显示当前帧
                self.show_frame(self.current_frame)
                
                # 获取用户输入
                cmd = input(f"\nCurrent frame: {self.current_frame} > ").strip().lower()
                
                if cmd in ['q', 'quit']:
                    break
                elif cmd in ['n', 'next']:
                    self.current_frame = min(self.current_frame + 1, self.total_frames - 1)
                elif cmd in ['p', 'prev']:
                    self.current_frame = max(self.current_frame - 1, 0)
                elif cmd in ['f', 'first']:
                    self.current_frame = 0
                elif cmd in ['l', 'last']:
                    self.current_frame = self.total_frames - 1
                elif cmd in ['s', 'stats']:
                    self.show_statistics()
                elif cmd.isdigit():
                    frame_num = int(cmd)
                    if 0 <= frame_num < self.total_frames:
                        self.current_frame = frame_num
                    else:
                        print(f"Invalid frame number. Range: 0-{self.total_frames-1}")
                elif cmd == '':
                    continue
                else:
                    print("Unknown command. Type 'q' to quit.")
                    
            except KeyboardInterrupt:
                print("\nExiting...")
                break
            except Exception as e:
                print(f"Error: {e}")
    
    def show_statistics(self):
        """显示数据集统计信息"""
        print(f"\n{'='*40}")
        print("Dataset Statistics")
        print(f"{'='*40}")
        
        with h5py.File(self.hdf5_path, 'r') as f:
            # 图像统计
            if 'observations/images' in f:
                img_group = f['observations/images']
                print(f"\nImage Data:")
                total_size = 0
                for cam_name in img_group.keys():
                    img_data = img_group[cam_name]
                    size_mb = img_data.nbytes / 1024 / 1024
                    total_size += size_mb
                    print(f"  {cam_name}: {img_data.shape} - {size_mb:.1f} MB")
                print(f"  Total image size: {total_size:.1f} MB")
            
            # 关节数据统计
            if 'observations/qpos' in f and NUMPY_AVAILABLE:
                qpos_data = f['observations/qpos'][:]
                print(f"\nJoint Position Statistics:")
                print(f"  Shape: {qpos_data.shape}")
                print(f"  Min: {np.min(qpos_data):.4f}")
                print(f"  Max: {np.max(qpos_data):.4f}")
                print(f"  Mean: {np.mean(qpos_data):.4f}")
                print(f"  Std: {np.std(qpos_data):.4f}")
                
                # 每个关节的统计
                print(f"  Per-joint statistics:")
                for i in range(min(10, qpos_data.shape[1])):  # 只显示前10个关节
                    joint_data = qpos_data[:, i]
                    print(f"    Joint {i}: min={np.min(joint_data):.3f}, "
                          f"max={np.max(joint_data):.3f}, "
                          f"mean={np.mean(joint_data):.3f}")
            
            # 动作数据统计
            if 'action' in f and NUMPY_AVAILABLE:
                action_data = f['action'][:]
                print(f"\nAction Statistics:")
                print(f"  Shape: {action_data.shape}")
                print(f"  Min: {np.min(action_data):.4f}")
                print(f"  Max: {np.max(action_data):.4f}")
                print(f"  Mean: {np.mean(action_data):.4f}")
                print(f"  Std: {np.std(action_data):.4f}")
                
                # 每个动作维度的统计
                print(f"  Per-action statistics:")
                for i in range(action_data.shape[1]):
                    action_dim = action_data[:, i]
                    print(f"    Action {i}: min={np.min(action_dim):.3f}, "
                          f"max={np.max(action_dim):.3f}, "
                          f"mean={np.mean(action_dim):.3f}")

def main():
    parser = argparse.ArgumentParser(description='Simple HDF5 robot dataset visualizer')
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument('--file', type=str, help='Path to specific HDF5 file')
    group.add_argument('--data_dir', type=str, help='Directory containing HDF5 files')
    
    args = parser.parse_args()
    
    if args.file:
        if not os.path.exists(args.file):
            print(f"Error: File {args.file} not found")
            return
        
        visualizer = SimpleHDF5Visualizer(args.file)
        visualizer.interactive_view()
    
    elif args.data_dir:
        if not os.path.exists(args.data_dir):
            print(f"Error: Directory {args.data_dir} not found")
            return
        
        # 列出所有HDF5文件
        hdf5_files = [f for f in os.listdir(args.data_dir) if f.endswith('.hdf5')]
        if not hdf5_files:
            print(f"No HDF5 files found in {args.data_dir}")
            return
        
        hdf5_files.sort()
        print(f"Found {len(hdf5_files)} HDF5 files:")
        for i, f in enumerate(hdf5_files):
            print(f"  {i}: {f}")
        
        # 让用户选择文件
        try:
            choice = input(f"Enter file number (0-{len(hdf5_files)-1}) or press Enter for first file: ")
            if choice.strip() == "":
                choice = 0
            else:
                choice = int(choice)
            
            if 0 <= choice < len(hdf5_files):
                file_path = os.path.join(args.data_dir, hdf5_files[choice])
                visualizer = SimpleHDF5Visualizer(file_path)
                visualizer.interactive_view()
            else:
                print("Invalid choice")
        except (ValueError, KeyboardInterrupt):
            print("Cancelled")

if __name__ == '__main__':
    main()
