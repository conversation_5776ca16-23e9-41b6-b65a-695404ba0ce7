#!/usr/bin/env python3
"""
测试action数据的脚本
检查转换后的HDF5文件中action数据是否正确

Usage:
    python test_action_data.py --file /path/to/file.hdf5
"""

import argparse
import h5py
import numpy as np

def analyze_action_data(file_path):
    """分析HDF5文件中的action数据"""
    print(f"Analyzing action data in: {file_path}")
    
    with h5py.File(file_path, 'r') as f:
        if 'action' not in f:
            print("❌ No 'action' dataset found in file")
            return
        
        action_data = f['action'][:]
        print(f"✅ Action dataset found: {action_data.shape}")
        
        # 基本统计
        total_elements = action_data.size
        nonzero_elements = np.count_nonzero(action_data)
        zero_elements = total_elements - nonzero_elements
        
        print(f"\n📊 Basic Statistics:")
        print(f"  Total elements:    {total_elements}")
        print(f"  Non-zero elements: {nonzero_elements} ({nonzero_elements/total_elements*100:.1f}%)")
        print(f"  Zero elements:     {zero_elements} ({zero_elements/total_elements*100:.1f}%)")
        print(f"  Min value:         {np.min(action_data):.6f}")
        print(f"  Max value:         {np.max(action_data):.6f}")
        print(f"  Mean value:        {np.mean(action_data):.6f}")
        print(f"  Std deviation:     {np.std(action_data):.6f}")
        
        # 按维度分析
        print(f"\n📈 Per-dimension Analysis:")
        action_names = [
            'L_Arm_1', 'L_Arm_2', 'L_Arm_3', 'L_Arm_4', 'L_Arm_5', 'L_Arm_6', 'L_Arm_7',
            'R_Arm_1', 'R_Arm_2', 'R_Arm_3', 'R_Arm_4', 'R_Arm_5', 'R_Arm_6', 'R_Arm_7',
            'L_Hand', 'R_Hand'
        ]
        
        for i in range(min(action_data.shape[1], len(action_names))):
            dim_data = action_data[:, i]
            nonzero_count = np.count_nonzero(dim_data)
            if nonzero_count > 0:
                print(f"  {action_names[i]:<8}: {nonzero_count:>4}/{len(dim_data)} non-zero "
                      f"(min: {np.min(dim_data):>8.3f}, max: {np.max(dim_data):>8.3f}, "
                      f"mean: {np.mean(dim_data):>8.3f})")
            else:
                print(f"  {action_names[i]:<8}: {nonzero_count:>4}/{len(dim_data)} non-zero (all zeros)")
        
        # 时间序列分析
        print(f"\n⏱️  Time Series Analysis:")
        
        # 找到第一个和最后一个非零帧
        nonzero_frames = np.any(action_data != 0, axis=1)
        if np.any(nonzero_frames):
            first_nonzero = np.argmax(nonzero_frames)
            last_nonzero = len(nonzero_frames) - 1 - np.argmax(nonzero_frames[::-1])
            active_frames = np.sum(nonzero_frames)
            
            print(f"  First non-zero frame: {first_nonzero}")
            print(f"  Last non-zero frame:  {last_nonzero}")
            print(f"  Active frames:        {active_frames}/{len(nonzero_frames)} "
                  f"({active_frames/len(nonzero_frames)*100:.1f}%)")
            
            # 显示一些样本数据
            print(f"\n📋 Sample Data (first 5 non-zero frames):")
            sample_count = 0
            for i, frame in enumerate(action_data):
                if np.any(frame != 0) and sample_count < 5:
                    print(f"  Frame {i:4d}: {frame}")
                    sample_count += 1
        else:
            print("  ❌ No non-zero frames found!")
        
        # 检查数据模式
        print(f"\n🔍 Data Pattern Analysis:")
        
        # 检查是否有重复的action值
        unique_rows = np.unique(action_data, axis=0)
        print(f"  Unique action patterns: {len(unique_rows)}")
        
        # 检查是否有常数值
        for i in range(action_data.shape[1]):
            dim_data = action_data[:, i]
            unique_values = np.unique(dim_data)
            if len(unique_values) == 1:
                print(f"  {action_names[i] if i < len(action_names) else f'Dim_{i}'} is constant: {unique_values[0]}")
            elif len(unique_values) <= 5:
                print(f"  {action_names[i] if i < len(action_names) else f'Dim_{i}'} has {len(unique_values)} unique values: {unique_values}")

def compare_with_other_data(file_path):
    """比较action数据与其他数据的关系"""
    print(f"\n🔗 Comparing with other datasets:")
    
    with h5py.File(file_path, 'r') as f:
        # 检查qpos数据
        if 'observations/qpos' in f:
            qpos_data = f['observations/qpos'][:]
            qpos_nonzero = np.count_nonzero(qpos_data)
            print(f"  QPos non-zero elements: {qpos_nonzero}/{qpos_data.size} "
                  f"({qpos_nonzero/qpos_data.size*100:.1f}%)")
        
        # 检查图像数据
        if 'observations/images' in f:
            img_group = f['observations/images']
            for cam_name in img_group.keys():
                img_data = img_group[cam_name]
                print(f"  {cam_name.title()} images: {img_data.shape}")
        
        # 检查数据长度一致性
        if 'action' in f and 'observations/qpos' in f:
            action_len = len(f['action'])
            qpos_len = len(f['observations/qpos'])
            if action_len == qpos_len:
                print(f"  ✅ Data length consistent: {action_len} frames")
            else:
                print(f"  ❌ Data length mismatch: action={action_len}, qpos={qpos_len}")

def main():
    parser = argparse.ArgumentParser(description='Analyze action data in HDF5 files')
    parser.add_argument('--file', type=str, required=True, help='Path to HDF5 file')
    
    args = parser.parse_args()
    
    try:
        analyze_action_data(args.file)
        compare_with_other_data(args.file)
        
        print(f"\n💡 Recommendations:")
        print("  1. If all action data is zero, check if action topics were published during recording")
        print("  2. If only some dimensions have data, verify topic names and message formats")
        print("  3. If data seems truncated, check time alignment and timeout settings")
        print("  4. Use debug_data_collection.py to monitor live data collection")
        
    except Exception as e:
        print(f"❌ Error analyzing file: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
