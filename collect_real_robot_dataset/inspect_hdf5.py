#!/usr/bin/env python3
"""
HDF5 数据检查脚本
快速查看HDF5文件的结构和基本统计信息

Usage:
    python inspect_hdf5.py --file /path/to/file.hdf5
    python inspect_hdf5.py --data_dir /home/<USER>/act-plus-plus/datasets/ym_touch
"""

import os
import argparse
import h5py
import numpy as np
import matplotlib.pyplot as plt

def print_hdf5_structure(file_path):
    """打印HDF5文件结构"""
    print(f"\n{'='*60}")
    print(f"File: {os.path.basename(file_path)}")
    print(f"{'='*60}")
    
    with h5py.File(file_path, 'r') as f:
        def print_item(name, obj, indent=""):
            if isinstance(obj, h5py.Dataset):
                print(f"{indent}{name}: {obj.shape} {obj.dtype}")
                if len(obj.shape) <= 2 and obj.size < 100:  # 小数据集显示一些值
                    print(f"{indent}  Sample values: {obj[...].flatten()[:10]}")
                elif len(obj.shape) > 0:
                    print(f"{indent}  Min: {np.min(obj):.4f}, Max: {np.max(obj):.4f}, Mean: {np.mean(obj):.4f}")
            elif isinstance(obj, h5py.Group):
                print(f"{indent}{name}/")
                for key in obj.keys():
                    print_item(key, obj[key], indent + "  ")
        
        print("Structure:")
        for key in f.keys():
            print_item(key, f[key])

def analyze_dataset(file_path):
    """分析数据集的详细信息"""
    with h5py.File(file_path, 'r') as f:
        print(f"\n{'='*40}")
        print("Dataset Analysis")
        print(f"{'='*40}")
        
        # 分析图像数据
        if 'observations/images' in f:
            img_group = f['observations/images']
            print(f"\nImage Data:")
            for cam_name in img_group.keys():
                img_data = img_group[cam_name]
                print(f"  {cam_name}: {img_data.shape} {img_data.dtype}")
                print(f"    Frames: {img_data.shape[0]}")
                print(f"    Resolution: {img_data.shape[1]}x{img_data.shape[2]}")
                print(f"    Channels: {img_data.shape[3] if len(img_data.shape) > 3 else 1}")
                print(f"    Size: {img_data.nbytes / 1024 / 1024:.1f} MB")
        
        # 分析关节数据
        if 'observations/qpos' in f:
            qpos = f['observations/qpos']
            print(f"\nJoint Position Data:")
            print(f"  Shape: {qpos.shape}")
            print(f"  Frames: {qpos.shape[0]}")
            print(f"  Joints: {qpos.shape[1]}")
            print(f"  Range: [{np.min(qpos):.4f}, {np.max(qpos):.4f}]")
            print(f"  Mean: {np.mean(qpos):.4f}")
            print(f"  Std: {np.std(qpos):.4f}")
        
        # 分析动作数据
        if 'action' in f:
            action = f['action']
            print(f"\nAction Data:")
            print(f"  Shape: {action.shape}")
            print(f"  Frames: {action.shape[0]}")
            print(f"  Action dims: {action.shape[1]}")
            print(f"  Range: [{np.min(action):.4f}, {np.max(action):.4f}]")
            print(f"  Mean: {np.mean(action):.4f}")
            print(f"  Std: {np.std(action):.4f}")

def plot_data_summary(file_path):
    """绘制数据摘要图表"""
    with h5py.File(file_path, 'r') as f:
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle(f'Data Summary: {os.path.basename(file_path)}')
        
        # 关节位置时间序列
        if 'observations/qpos' in f:
            qpos = f['observations/qpos'][:]
            ax = axes[0, 0]
            ax.plot(qpos[:, :min(10, qpos.shape[1])])  # 只显示前10个关节
            ax.set_title('Joint Positions Over Time')
            ax.set_xlabel('Frame')
            ax.set_ylabel('Position')
            ax.grid(True, alpha=0.3)
            ax.legend([f'Joint {i}' for i in range(min(10, qpos.shape[1]))], 
                     bbox_to_anchor=(1.05, 1), loc='upper left')
        
        # 动作时间序列
        if 'action' in f:
            action = f['action'][:]
            ax = axes[0, 1]
            ax.plot(action)
            ax.set_title('Actions Over Time')
            ax.set_xlabel('Frame')
            ax.set_ylabel('Action Value')
            ax.grid(True, alpha=0.3)
        
        # 关节位置分布
        if 'observations/qpos' in f:
            qpos = f['observations/qpos'][:]
            ax = axes[1, 0]
            ax.hist(qpos.flatten(), bins=50, alpha=0.7)
            ax.set_title('Joint Position Distribution')
            ax.set_xlabel('Position')
            ax.set_ylabel('Frequency')
            ax.grid(True, alpha=0.3)
        
        # 动作分布
        if 'action' in f:
            action = f['action'][:]
            ax = axes[1, 1]
            ax.hist(action.flatten(), bins=50, alpha=0.7)
            ax.set_title('Action Distribution')
            ax.set_xlabel('Action Value')
            ax.set_ylabel('Frequency')
            ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()

def compare_datasets(data_dir):
    """比较多个数据集的统计信息"""
    hdf5_files = [f for f in os.listdir(data_dir) if f.endswith('.hdf5')]
    hdf5_files.sort()
    
    print(f"\n{'='*60}")
    print(f"Dataset Comparison ({len(hdf5_files)} files)")
    print(f"{'='*60}")
    
    stats = []
    for file_name in hdf5_files:
        file_path = os.path.join(data_dir, file_name)
        with h5py.File(file_path, 'r') as f:
            stat = {'name': file_name}
            
            # 获取基本信息
            if 'observations/qpos' in f:
                qpos = f['observations/qpos']
                stat['frames'] = qpos.shape[0]
                stat['joints'] = qpos.shape[1]
            
            if 'action' in f:
                action = f['action']
                stat['action_dims'] = action.shape[1]
            
            # 图像信息
            if 'observations/images' in f:
                img_group = f['observations/images']
                stat['cameras'] = len(img_group.keys())
                # 获取第一个相机的信息
                first_cam = list(img_group.keys())[0]
                img_shape = img_group[first_cam].shape
                stat['img_resolution'] = f"{img_shape[1]}x{img_shape[2]}"
            
            # 文件大小
            stat['size_mb'] = os.path.getsize(file_path) / 1024 / 1024
            
            stats.append(stat)
    
    # 打印比较表
    print(f"{'File':<25} {'Frames':<8} {'Joints':<8} {'Actions':<8} {'Cameras':<8} {'Resolution':<12} {'Size(MB)':<10}")
    print("-" * 90)
    
    for stat in stats:
        print(f"{stat['name']:<25} "
              f"{stat.get('frames', 'N/A'):<8} "
              f"{stat.get('joints', 'N/A'):<8} "
              f"{stat.get('action_dims', 'N/A'):<8} "
              f"{stat.get('cameras', 'N/A'):<8} "
              f"{stat.get('img_resolution', 'N/A'):<12} "
              f"{stat.get('size_mb', 0):<10.1f}")

def main():
    parser = argparse.ArgumentParser(description='Inspect HDF5 robot dataset')
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument('--file', type=str, help='Path to specific HDF5 file')
    group.add_argument('--data_dir', type=str, help='Directory containing HDF5 files')
    parser.add_argument('--plot', action='store_true', help='Show plots')
    
    args = parser.parse_args()
    
    if args.file:
        if not os.path.exists(args.file):
            print(f"Error: File {args.file} not found")
            return
        
        print_hdf5_structure(args.file)
        analyze_dataset(args.file)
        
        if args.plot:
            plot_data_summary(args.file)
    
    elif args.data_dir:
        if not os.path.exists(args.data_dir):
            print(f"Error: Directory {args.data_dir} not found")
            return
        
        # 比较所有数据集
        compare_datasets(args.data_dir)
        
        # 让用户选择一个文件进行详细分析
        hdf5_files = [f for f in os.listdir(args.data_dir) if f.endswith('.hdf5')]
        if hdf5_files:
            hdf5_files.sort()
            print(f"\nSelect a file for detailed analysis:")
            for i, f in enumerate(hdf5_files[:10]):  # 只显示前10个
                print(f"  {i}: {f}")
            
            try:
                choice = input(f"Enter file number (0-{min(9, len(hdf5_files)-1)}) or press Enter to skip: ")
                if choice.strip() != "":
                    choice = int(choice)
                    if 0 <= choice < len(hdf5_files):
                        file_path = os.path.join(args.data_dir, hdf5_files[choice])
                        print_hdf5_structure(file_path)
                        analyze_dataset(file_path)
                        
                        if args.plot:
                            plot_data_summary(file_path)
            except (ValueError, KeyboardInterrupt):
                print("Skipped detailed analysis")

if __name__ == '__main__':
    main()
