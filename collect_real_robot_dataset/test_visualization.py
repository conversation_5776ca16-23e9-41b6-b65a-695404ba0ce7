#!/usr/bin/env python3
"""
测试可视化脚本
快速测试HDF5可视化功能

Usage:
    python test_visualization.py
"""

import os
import sys

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from visualize_hdf5 import HDF5Visualizer

def main():
    # 数据目录
    data_dir = "/home/<USER>/act-plus-plus/datasets/ym_touch"
    
    if not os.path.exists(data_dir):
        print(f"Error: Directory {data_dir} not found")
        return
    
    # 获取HDF5文件列表
    hdf5_files = [f for f in os.listdir(data_dir) if f.endswith('.hdf5')]
    if not hdf5_files:
        print(f"No HDF5 files found in {data_dir}")
        return
    
    hdf5_files.sort()
    
    # 选择第一个文件进行测试
    test_file = os.path.join(data_dir, hdf5_files[0])
    print(f"Testing visualization with: {test_file}")
    
    try:
        # 创建可视化器
        visualizer = HDF5Visualizer(test_file)
        
        # 显示界面
        print("Launching visualization interface...")
        print("Controls:")
        print("  - Use slider to navigate frames")
        print("  - Click 'Play' to animate")
        print("  - Click 'Reset' to go back to frame 0")
        print("  - Close window to exit")
        
        visualizer.show()
        
    except Exception as e:
        print(f"Error during visualization: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
