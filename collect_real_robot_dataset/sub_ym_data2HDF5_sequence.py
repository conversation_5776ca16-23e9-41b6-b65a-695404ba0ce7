#!/usr/bin/env python3
"""
基于序列号的ROS2 bag转HDF5脚本
使用序列号而不是时间戳进行数据对齐，避免时间戳不一致问题

Usage:
  python sub_ym_data2HDF5_sequence.py \
      --parent_dir /path/to/ros2bags \
      --output_dir ./HDF5 \
      [--timeout 10]
"""
import os
import time
import argparse
import threading
import subprocess

import h5py
import numpy as np
import cv2
import rclpy
from rclpy.node import Node
from sensor_msgs.msg import JointState, Image
from std_msgs.msg import Float64MultiArray, String, Int32MultiArray
from cv_bridge import CvBridge

# Define 14 arm joint names
ARM_JOINTS = [f'Left_Arm_Joint{i}' for i in range(1,8)] + [f'Right_Arm_Joint{i}' for i in range(1,8)]
# Map hand commands to discrete values
HAND_MAP = {'release': 0, 'grasp_high_power': 1}

class SequenceRecorder(Node):
    def __init__(self, timeout=10.0):
        super().__init__('sequence_recorder')
        self.timeout = timeout
        self.last_msg_time = self.get_clock().now()
        self.close_flag = False
        self.bridge = CvBridge()
        
        # 使用序列号而不是时间戳
        self.sequence_counter = 0
        self.data_lock = threading.Lock()
        
        # 数据缓冲区 - 使用序列号作为键
        self.data_sequences = {}
        
        # 最新的action状态，用于填充缺失的帧
        self.latest_actions = {
            'left_arm_cmd': [0.0] * 7,
            'right_arm_cmd': [0.0] * 7,
            'left_hand_cmd': 0,
            'right_hand_cmd': 0,
            'left_gripper': [0] * 6,  # 默认6个gripper值
            'right_gripper': [0] * 6,
        }
        
        # Subscriptions
        self.create_subscription(Image, '/camera/color/image_raw',          self.cb_head_image,   10)
        self.create_subscription(Image, '/camera1/camera1/color/image_raw', self.cb_left_image,   10)
        self.create_subscription(Image, '/camera2/camera2/color/image_raw', self.cb_right_image,  10)
        self.create_subscription(JointState, '/joint_states',                self.cb_joint_states, 10)
        self.create_subscription(Float64MultiArray,
                                 '/left_arm_position_controller/commands', self.cb_left_arm_cmd,  10)
        self.create_subscription(Float64MultiArray,
                                 '/right_arm_position_controller/commands',self.cb_right_arm_cmd, 10)
        self.create_subscription(String, '/left_hand_command',   self.cb_left_hand_cmd,  10)
        self.create_subscription(String, '/right_hand_command',  self.cb_right_hand_cmd, 10)
        self.create_subscription(Int32MultiArray, '/left_gripper_position',  self.cb_left_gripper,  10)
        self.create_subscription(Int32MultiArray, '/right_gripper_position', self.cb_right_gripper, 10)
        
        # Timer to detect timeout and trigger sequence recording
        self.create_timer(0.1, self.record_sequence)  # 10Hz recording rate
        self.create_timer(1.0, self.check_timeout)

    def check_timeout(self):
        now = self.get_clock().now()
        if (now.nanoseconds - self.last_msg_time.nanoseconds) * 1e-9 > self.timeout:
            self.get_logger().info('Timeout reached, stopping recording')
            self.close_flag = True

    def pad_image(self, img):
        # pad height from HxW to 480x640, center vertically
        h, w = img.shape[:2]
        target_h, target_w = 480, 640
        top = (target_h - h) // 2
        bottom = target_h - h - top
        left = (target_w - w) // 2
        right = target_w - w - left
        # pad with black
        return cv2.copyMakeBorder(img, top, bottom, left, right,
                                  cv2.BORDER_CONSTANT, value=[0,0,0])

    def record_sequence(self):
        """定期记录当前状态序列"""
        with self.data_lock:
            # 检查是否有新的图像数据作为触发
            if hasattr(self, 'latest_images') and self.latest_images:
                seq_data = {
                    'sequence': self.sequence_counter,
                    'images': self.latest_images.copy(),
                    'joints': getattr(self, 'latest_joints', [0.0] * len(ARM_JOINTS)),
                    'actions': self.latest_actions.copy(),
                    'grippers': {
                        'left': getattr(self, 'latest_left_gripper', [0] * 6),
                        'right': getattr(self, 'latest_right_gripper', [0] * 6),
                    }
                }
                
                self.data_sequences[self.sequence_counter] = seq_data
                self.sequence_counter += 1
                
                # 清除图像缓存，等待下一帧
                self.latest_images = {}

    # Image callbacks - 作为主要触发器
    def cb_head_image(self, msg: Image):
        img = self.bridge.imgmsg_to_cv2(msg, 'bgr8')
        img = self.pad_image(img)
        
        with self.data_lock:
            if not hasattr(self, 'latest_images'):
                self.latest_images = {}
            self.latest_images['head'] = img
        
        self.last_msg_time = self.get_clock().now()

    def cb_left_image(self, msg: Image):
        img = self.bridge.imgmsg_to_cv2(msg, 'bgr8')
        img = self.pad_image(img)
        
        with self.data_lock:
            if not hasattr(self, 'latest_images'):
                self.latest_images = {}
            self.latest_images['left'] = img
        
        self.last_msg_time = self.get_clock().now()

    def cb_right_image(self, msg: Image):
        img = self.bridge.imgmsg_to_cv2(msg, 'bgr8')
        img = self.pad_image(img)
        
        with self.data_lock:
            if not hasattr(self, 'latest_images'):
                self.latest_images = {}
            self.latest_images['right'] = img
        
        self.last_msg_time = self.get_clock().now()

    # Joint state callback
    def cb_joint_states(self, msg: JointState):
        pos = []
        for name in ARM_JOINTS:
            try: 
                idx = msg.name.index(name)
                pos.append(msg.position[idx])
            except ValueError: 
                pos.append(0.0)
        
        with self.data_lock:
            self.latest_joints = pos
        
        self.last_msg_time = self.get_clock().now()

    # Action callbacks - 更新最新状态
    def cb_left_arm_cmd(self, msg: Float64MultiArray):
        with self.data_lock:
            self.latest_actions['left_arm_cmd'] = list(msg.data)
        self.last_msg_time = self.get_clock().now()

    def cb_right_arm_cmd(self, msg: Float64MultiArray):
        with self.data_lock:
            self.latest_actions['right_arm_cmd'] = list(msg.data)
        self.last_msg_time = self.get_clock().now()

    def cb_left_hand_cmd(self, msg: String):
        v = HAND_MAP.get(msg.data, 0)
        with self.data_lock:
            self.latest_actions['left_hand_cmd'] = v
        self.last_msg_time = self.get_clock().now()

    def cb_right_hand_cmd(self, msg: String):
        v = HAND_MAP.get(msg.data, 0)
        with self.data_lock:
            self.latest_actions['right_hand_cmd'] = v
        self.last_msg_time = self.get_clock().now()

    # Gripper state callbacks
    def cb_left_gripper(self, msg: Int32MultiArray):
        with self.data_lock:
            self.latest_left_gripper = list(msg.data)
            self.latest_actions['left_gripper'] = list(msg.data)
        self.last_msg_time = self.get_clock().now()

    def cb_right_gripper(self, msg: Int32MultiArray):
        with self.data_lock:
            self.latest_right_gripper = list(msg.data)
            self.latest_actions['right_gripper'] = list(msg.data)
        self.last_msg_time = self.get_clock().now()

# Build arrays from sequence data
def build_arrays_from_sequences(sequences):
    """从序列数据构建数组"""
    if not sequences:
        raise RuntimeError('No sequence data recorded')
    
    # 按序列号排序
    sorted_sequences = sorted(sequences.items())
    T = len(sorted_sequences)
    
    print(f"Building arrays from {T} sequences...")
    
    # 获取数据维度
    first_seq = sorted_sequences[0][1]
    
    # 确定gripper维度
    left_gripper_dim = len(first_seq['grippers']['left'])
    right_gripper_dim = len(first_seq['grippers']['right'])
    
    # Image dims
    H, W = 480, 640
    
    # Allocate arrays
    head = np.zeros((T, H, W, 3), dtype=np.uint8)
    left = np.zeros((T, H, W, 3), dtype=np.uint8)
    right = np.zeros((T, H, W, 3), dtype=np.uint8)
    qpos = np.zeros((T, len(ARM_JOINTS) + left_gripper_dim + right_gripper_dim), dtype=np.float32)
    action = np.zeros((T, 16), dtype=np.float32)
    
    # Fill arrays
    for i, (seq_num, seq_data) in enumerate(sorted_sequences):
        # Images
        if 'head' in seq_data['images']:
            head[i] = seq_data['images']['head']
        if 'left' in seq_data['images']:
            left[i] = seq_data['images']['left']
        if 'right' in seq_data['images']:
            right[i] = seq_data['images']['right']
        
        # Joint positions
        qpos[i, :len(ARM_JOINTS)] = seq_data['joints']
        
        # Gripper positions
        qpos[i, len(ARM_JOINTS):len(ARM_JOINTS)+left_gripper_dim] = seq_data['grippers']['left']
        qpos[i, len(ARM_JOINTS)+left_gripper_dim:] = seq_data['grippers']['right']
        
        # Actions
        actions = seq_data['actions']
        action[i, 0:7] = actions['left_arm_cmd']
        action[i, 7:14] = actions['right_arm_cmd']
        action[i, 14] = actions['left_hand_cmd']
        action[i, 15] = actions['right_hand_cmd']
    
    print(f"Arrays built successfully. Shape: images={head.shape}, qpos={qpos.shape}, action={action.shape}")
    
    # 统计信息
    action_nonzero = np.count_nonzero(action)
    print(f"Action data: {action_nonzero}/{action.size} non-zero elements ({action_nonzero/action.size*100:.1f}%)")
    
    return head, left, right, qpos, action

def write_hdf5(head, left, right, qpos, action, path):
    os.makedirs(os.path.dirname(path), exist_ok=True)
    with h5py.File(path, 'w') as f:
        # root-level action
        f.create_dataset('action', data=action)
        # observations group
        obs = f.create_group('observations')
        img_grp = obs.create_group('images')
        img_grp.create_dataset('head', data=head, dtype='uint8', chunks=True)
        img_grp.create_dataset('left', data=left, dtype='uint8', chunks=True)
        img_grp.create_dataset('right', data=right, dtype='uint8', chunks=True)
        # qpos under observations
        obs.create_dataset('qpos', data=qpos)
        zero_qvel = np.zeros_like(qpos)
        obs.create_dataset('qvel', data=zero_qvel)
    print(f"Saved HDF5 to {path}")

def play_bag(path):
    time.sleep(2.0)
    subprocess.Popen(['ros2', 'bag', 'play', path])

# Main entry
if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('--parent_dir', required=True)
    parser.add_argument('--output_dir', default='./HDF5_sequence')
    parser.add_argument('--timeout', type=float, default=10.0)
    args = parser.parse_args()

    bags = sorted([d for d in os.listdir(args.parent_dir)
                   if os.path.isdir(os.path.join(args.parent_dir, d))])
    os.makedirs(args.output_dir, exist_ok=True)

    for bag in bags:
        bag_dir = os.path.join(args.parent_dir, bag)
        if not os.path.exists(os.path.join(bag_dir, 'metadata.yaml')):
            continue
        
        print(f"Processing bag: {bag}")
        threading.Thread(target=play_bag, args=(bag_dir,), daemon=True).start()
        rclpy.init()
        recorder = SequenceRecorder(timeout=args.timeout)
        
        while rclpy.ok() and not recorder.close_flag:
            rclpy.spin_once(recorder)
        
        sequences = recorder.data_sequences
        recorder.destroy_node()
        rclpy.shutdown()

        if sequences:
            head, left, right, qpos, action = build_arrays_from_sequences(sequences)
            out_path = os.path.join(args.output_dir, f"{bag}.hdf5")
            write_hdf5(head, left, right, qpos, action, out_path)
        else:
            print(f"No data recorded for bag {bag}")
