#!/usr/bin/env python3
"""
Convert ROS2 bag recordings to HDF5 dataset for ACT training including padded images.

The HDF5 layout:
  /images/head   : [T, 480, 640, 3] uint8
  /images/left   : [T, 480, 640, 3] uint8
  /images/right  : [T, 480, 640, 3] uint8
  /qpos          : [T, 26] float32  (14 arm joints + left gripper N_l + right gripper N_r)
  /action        : [T, 16] float32  (7 left arm + 7 right arm + left hand + right hand)

Images originally come as 640x400 RGB and are padded vertically to 640x480 with black borders.

Usage:
  python convert_ros2bag_to_hdf5.py \
      --parent_dir /path/to/ros2bags \
      --output_dir ./HDF5 \
      [--timeout 10]
"""
import os
import time
import argparse
import threading
import subprocess

import h5py
import numpy as np
import cv2
import rclpy
from rclpy.node import Node
from sensor_msgs.msg import JointState, Image
from std_msgs.msg import Float64MultiArray, String, Int32MultiArray
from cv_bridge import CvBridge

# Define 14 arm joint names
ARM_JOINTS = [f'Left_Arm_Joint{i}' for i in range(1,8)] + [f'Right_Arm_Joint{i}' for i in range(1,8)]
# Map hand commands to discrete values
HAND_MAP = {'release': 0, 'grasp_high_power': 1}

class TopicRecorder(Node):
    def __init__(self, timeout=10.0):
        super().__init__('topic_recorder')
        self.timeout = timeout
        self.last_msg_time = self.get_clock().now()
        self.close_flag = False
        self.bridge = CvBridge()
        # Buffers for data streams
        self.buffers = {
            'images_head': [],
            'images_left': [],
            'images_right': [],
            'joints': [],
            'left_arm_cmd': [],
            'right_arm_cmd': [],
            'left_hand_cmd': [],
            'right_hand_cmd': [],
            'left_gripper': [],
            'right_gripper': [],
        }
        # Subscriptions
        self.create_subscription(Image, '/camera/color/image_raw',          self.cb_head_image,   10)
        self.create_subscription(Image, '/camera1/camera1/color/image_raw', self.cb_left_image,   10)
        self.create_subscription(Image, '/camera2/camera2/color/image_raw', self.cb_right_image,  10)
        self.create_subscription(JointState, '/joint_states',                self.cb_joint_states, 10)
        self.create_subscription(Float64MultiArray,
                                 '/left_arm_position_controller/commands', self.cb_left_arm_cmd,  10)
        self.create_subscription(Float64MultiArray,
                                 '/right_arm_position_controller/commands',self.cb_right_arm_cmd, 10)
        self.create_subscription(String, '/left_hand_command',   self.cb_left_hand_cmd,  10)
        self.create_subscription(String, '/right_hand_command',  self.cb_right_hand_cmd, 10)
        self.create_subscription(Int32MultiArray, '/left_gripper_position',  self.cb_left_gripper,  10)
        self.create_subscription(Int32MultiArray, '/right_gripper_position', self.cb_right_gripper, 10)
        # Timer to detect timeout
        self.create_timer(1.0, self.check_timeout)

    def check_timeout(self):
        now = self.get_clock().now()
        if (now.nanoseconds - self.last_msg_time.nanoseconds) * 1e-9 > self.timeout:
            self.get_logger().info('Timeout reached, stopping recording')
            self.close_flag = True

    def pad_image(self, img):
        # pad height from HxW to 480x640, center vertically
        h, w = img.shape[:2]
        target_h, target_w = 480, 640
        top = (target_h - h) // 2
        bottom = target_h - h - top
        left = (target_w - w) // 2
        right = target_w - w - left
        # pad with black
        return cv2.copyMakeBorder(img, top, bottom, left, right,
                                  cv2.BORDER_CONSTANT, value=[0,0,0])

    # Image callbacks
    def cb_head_image(self, msg: Image):
        img = self.bridge.imgmsg_to_cv2(msg, 'bgr8')
        img = self.pad_image(img)
        ts = msg.header.stamp.sec * 1e9 + msg.header.stamp.nanosec
        self.buffers['images_head'].append({'time': ts, 'img': img})
        self.last_msg_time = self.get_clock().now()

    def cb_left_image(self, msg: Image):
        img = self.bridge.imgmsg_to_cv2(msg, 'bgr8')
        img = self.pad_image(img)
        ts = msg.header.stamp.sec * 1e9 + msg.header.stamp.nanosec
        self.buffers['images_left'].append({'time': ts, 'img': img})
        self.last_msg_time = self.get_clock().now()

    def cb_right_image(self, msg: Image):
        img = self.bridge.imgmsg_to_cv2(msg, 'bgr8')
        img = self.pad_image(img)
        ts = msg.header.stamp.sec * 1e9 + msg.header.stamp.nanosec
        self.buffers['images_right'].append({'time': ts, 'img': img})
        self.last_msg_time = self.get_clock().now()

    # Joint state callback
    def cb_joint_states(self, msg: JointState):
        ts = msg.header.stamp.sec * 1e9 + msg.header.stamp.nanosec
        pos = []
        for name in ARM_JOINTS:
            try: idx = msg.name.index(name); pos.append(msg.position[idx])
            except ValueError: pos.append(0.0)
        self.buffers['joints'].append({'time': ts, 'pos': pos})
        self.last_msg_time = self.get_clock().now()

    # Action callbacks
    def cb_left_arm_cmd(self, msg: Float64MultiArray):
        ts = msg.layout.data_offset or self.get_clock().now().nanoseconds
        self.buffers['left_arm_cmd'].append({'time': ts, 'vals': list(msg.data)})
        self.last_msg_time = self.get_clock().now()

    def cb_right_arm_cmd(self, msg: Float64MultiArray):
        ts = msg.layout.data_offset or self.get_clock().now().nanoseconds
        self.buffers['right_arm_cmd'].append({'time': ts, 'vals': list(msg.data)})
        self.last_msg_time = self.get_clock().now()

    def cb_left_hand_cmd(self, msg: String):
        ts = self.get_clock().now().nanoseconds
        v = HAND_MAP.get(msg.data, 0)
        self.buffers['left_hand_cmd'].append({'time': ts, 'val': v})
        self.last_msg_time = self.get_clock().now()

    def cb_right_hand_cmd(self, msg: String):
        ts = self.get_clock().now().nanoseconds
        v = HAND_MAP.get(msg.data, 0)
        self.buffers['right_hand_cmd'].append({'time': ts, 'val': v})
        self.last_msg_time = self.get_clock().now()

    # Gripper state callbacks
    def cb_left_gripper(self, msg: Int32MultiArray):
        ts = self.get_clock().now().nanoseconds
        self.buffers['left_gripper'].append({'time': ts, 'vals': list(msg.data)})
        self.last_msg_time = self.get_clock().now()

    def cb_right_gripper(self, msg: Int32MultiArray):
        ts = self.get_clock().now().nanoseconds
        self.buffers['right_gripper'].append({'time': ts, 'vals': list(msg.data)})
        self.last_msg_time = self.get_clock().now()

def find_closest_data(target_time, data_list, tolerance=1e8):
    """
    Find the data point with timestamp closest to target_time.

    Args:
        target_time: Target timestamp in nanoseconds
        data_list: List of data dictionaries with 'time' key
        tolerance: Maximum time difference allowed in nanoseconds (default: 0.1s)

    Returns:
        The closest data point or None if no data within tolerance
    """
    if not data_list:
        return None

    best_data = None
    best_diff = float('inf')

    for data in data_list:
        diff = abs(data['time'] - target_time)
        if diff < best_diff:
            best_diff = diff
            best_data = data

    # Return None if the closest data is beyond tolerance
    if best_diff > tolerance:
        return None

    return best_data

# Build arrays for images, qpos, action with time alignment
def build_arrays(bufs):
    # Choose image stream as reference timeline (images have lower frequency)
    # Priority: head -> left -> right
    reference_images = None
    ref_stream_name = None

    if bufs['images_head']:
        reference_images = bufs['images_head']
        ref_stream_name = 'images_head'
    elif bufs['images_left']:
        reference_images = bufs['images_left']
        ref_stream_name = 'images_left'
    elif bufs['images_right']:
        reference_images = bufs['images_right']
        ref_stream_name = 'images_right'
    else:
        raise RuntimeError('No image data received, cannot build arrays')

    T = len(reference_images)
    print(f"Using {ref_stream_name} as reference timeline with {T} frames")

    # Determine gripper dimensions, default to 6 if no data
    DEFAULT_GRIPPER_DOF = 6
    if bufs['left_gripper']:
        Nl = len(bufs['left_gripper'][0]['vals'])
    else:
        Nl = DEFAULT_GRIPPER_DOF
    if bufs['right_gripper']:
        Nr = len(bufs['right_gripper'][0]['vals'])
    else:
        Nr = DEFAULT_GRIPPER_DOF

    # Image dims
    H, W = 480, 640

    # Allocate arrays
    head = np.zeros((T, H, W, 3), dtype=np.uint8)
    left = np.zeros((T, H, W, 3), dtype=np.uint8)
    right = np.zeros((T, H, W, 3), dtype=np.uint8)
    qpos = np.zeros((T, len(ARM_JOINTS) + Nl + Nr), dtype=np.float32)
    action = np.zeros((T, 16), dtype=np.float32)

    # Use image timestamps as the reference timeline
    print(f"Processing {T} timesteps with time alignment based on {ref_stream_name}...")

    for i in range(T):
        # Get reference timestamp from the chosen image stream
        ref_time = reference_images[i]['time']

        # Fill images (reference stream is guaranteed to have data)
        if ref_stream_name == 'images_head':
            head[i] = reference_images[i]['img']
        else:
            head_data = find_closest_data(ref_time, bufs['images_head'])
            if head_data is not None:
                head[i] = head_data['img']

        if ref_stream_name == 'images_left':
            left[i] = reference_images[i]['img']
        else:
            left_data = find_closest_data(ref_time, bufs['images_left'])
            if left_data is not None:
                left[i] = left_data['img']

        if ref_stream_name == 'images_right':
            right[i] = reference_images[i]['img']
        else:
            right_data = find_closest_data(ref_time, bufs['images_right'])
            if right_data is not None:
                right[i] = right_data['img']

        # qpos: arm joints (time-aligned to image timestamps)
        joint_data = find_closest_data(ref_time, bufs['joints'])
        if joint_data is not None:
            qpos[i, :len(ARM_JOINTS)] = joint_data['pos']

        # qpos: grippers (time-aligned)
        left_gripper_data = find_closest_data(ref_time, bufs['left_gripper'])
        if left_gripper_data is not None:
            qpos[i, len(ARM_JOINTS):len(ARM_JOINTS)+Nl] = left_gripper_data['vals']

        right_gripper_data = find_closest_data(ref_time, bufs['right_gripper'])
        if right_gripper_data is not None:
            qpos[i, len(ARM_JOINTS)+Nl:] = right_gripper_data['vals']

        # actions: arms and hands (time-aligned)
        left_arm_data = find_closest_data(ref_time, bufs['left_arm_cmd'])
        if left_arm_data is not None:
            action[i, 0:7] = left_arm_data['vals']

        right_arm_data = find_closest_data(ref_time, bufs['right_arm_cmd'])
        if right_arm_data is not None:
            action[i, 7:14] = right_arm_data['vals']

        left_hand_data = find_closest_data(ref_time, bufs['left_hand_cmd'])
        if left_hand_data is not None:
            action[i, 14] = left_hand_data['val']

        right_hand_data = find_closest_data(ref_time, bufs['right_hand_cmd'])
        if right_hand_data is not None:
            action[i, 15] = right_hand_data['val']

    print("Time alignment completed.")
    return head, left, right, qpos, action

def write_hdf5(head, left, right, qpos, action, path):
    os.makedirs(os.path.dirname(path), exist_ok=True)
    with h5py.File(path, 'w') as f:
        # root-level action
        f.create_dataset('action', data=action)
        # observations group
        obs = f.create_group('observations')
        img_grp = obs.create_group('images')
        img_grp.create_dataset('head', data=head, dtype='uint8', chunks=True)
        img_grp.create_dataset('left', data=left, dtype='uint8', chunks=True)
        img_grp.create_dataset('right', data=right, dtype='uint8', chunks=True)
        # qpos under observations
        obs.create_dataset('qpos', data=qpos)
        zero_qvel = np.zeros_like(qpos)
        obs.create_dataset('qvel', data=zero_qvel)
    print(f"Saved HDF5 to {path}")

def play_bag(path):
    time.sleep(2.0)
    subprocess.Popen(['ros2', 'bag', 'play', path])

# Main entry
if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('--parent_dir', required=True)
    parser.add_argument('--output_dir', default='./HDF5')
    parser.add_argument('--timeout', type=float, default=10.0)
    args = parser.parse_args()

    bags = sorted([d for d in os.listdir(args.parent_dir)
                   if os.path.isdir(os.path.join(args.parent_dir, d))])
    os.makedirs(args.output_dir, exist_ok=True)

    for bag in bags:
        bag_dir = os.path.join(args.parent_dir, bag)
        if not os.path.exists(os.path.join(bag_dir, 'metadata.yaml')):
            continue
        threading.Thread(target=play_bag, args=(bag_dir,), daemon=True).start()
        rclpy.init()
        recorder = TopicRecorder(timeout=args.timeout)
        while rclpy.ok() and not recorder.close_flag:
            rclpy.spin_once(recorder)
        data = recorder.buffers
        recorder.destroy_node()
        rclpy.shutdown()

        head, left, right, qpos, action = build_arrays(data)
        out_path = os.path.join(args.output_dir, f"{bag}.hdf5")
        write_hdf5(head, left, right, qpos, action, out_path)

