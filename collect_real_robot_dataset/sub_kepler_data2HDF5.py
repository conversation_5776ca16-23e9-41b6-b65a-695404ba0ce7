# ------------------------------------------
# 2024.10.22
# 实现从ros2 topic中读取数据，并保存到HDF5文件中
# 1.建立ROS2节点，建立通讯，10s内未读取到topic数据则退出
# 2.订阅topic，获取数据（需要机器人 或者 ros bag 发布），将完整数据保存至dict中
# 3.将数据的时间戳对齐(初始帧对齐，将结尾多余的帧删除)
# 4.读取需要的数据转化为标准数据集格式，保存到HDF5文件中
# 5.joint_states 和 joint_action 的数据顺序不一致， 按需取手臂14关节数据记录（08.31）
# 运行方式，5s内运行发送的topic程序 ：python /home/<USER>/workspace/program/robotROS2_ws/src/example_py/example_py/sub_kepler_data2HDF5.py
# 从ros2bag中的topic读取数据保存到hdf5文件 保存左目及uint16深度图数据
# ------------------------------------------
import os
import time
import h5py
import argparse
import numpy as np
from tqdm import tqdm
import json
import rclpy
from rclpy.node import Node
from rclpy.time import Time
from rosgraph_msgs.msg import Clock
from sensor_msgs.msg import JointState, CameraInfo, Image, Imu
from std_msgs.msg import String  # std_msgs是ROS内置的数据类型库，里面有msg、srv等一系列基础的数据类型
from cv_bridge import CvBridge, CvBridgeError
import cv2
import matplotlib.pyplot as plt
from collections import deque

class TopicDemoSubscriber(Node):
    def __init__(self):
        super().__init__("sub_isaacsim_node")

        self.start_time = time.time()
        self.last_time=0
        self.ros2_clock = 0.0
        self.num_dof = 30
        self.action_dict = {"time": 0,
                            "qpos": [0] * self.num_dof,
                            "qvel": [0] * self.num_dof,
                            "effort":[0] * self.num_dof}
        self.state_dict = {"time": 0,
                           "qpos": [0] * self.num_dof,
                           "qvel": [0] * self.num_dof,
                           "effort":[0] * self.num_dof}
        self.data_dict = { 'data_clock': [],
                            'data_joints': [],
                            'data_action': [],
                            'data_images_LeftRGB': [],
                            'data_images_LeftIR': [],
                            'data_images_LeftDepth': [],
                            'data_images_LeftDepthRGB': [],
                            'data_images_RightIR': [],
                            'data_imu': [],
        }

        self.close_flag = False
        self.saveImg_flag = False   # 是否保存每帧图像至 ./image/*.jpg 
        self.last_message_time = self.get_clock().now()
        self.timer = self.create_timer(0.1, self.timer_callback)    # 不断更新时间的定时器

        # # isaac_joint_actions 记录  # 动捕设备真机遥操作 remote_joint_commands # 动捕设备采集 isaac_joint_commands    # 仿真中数据采集 isaac_joint_actions
        self.joint_action_subscription = self.create_subscription(
            JointState, 'xsens/remote_joint_commands', self.joint_actions_callback, 10
        )
        self.joint_action_subscription

        self.joint_state_subscription = self.create_subscription(
            JointState, 'upper_body_data', self.joint_state_callback, 10
        )
        self.joint_state_subscription

        self.bridge = CvBridge()

        # 注意此处不是subscriber，而是subscription
        # 数据类型，话题名，回调函数，队列长度

        # isaac sim 在仿真中合成数据时有 /clock 话题，但真机不发布 /clock 话题
        # self.clock_subscription = self.create_subscription(
        #     Clock, 'clock', self.clock_callback, 10
        # )
        # self.clock_subscription  # prevent unused variable warning


        # 订阅者的构造函数和回调函数不需要定时器Timer，因为当收到Message时将启动回调函数        
        self.RGBimgLeft_subscription = self.create_subscription(
            Image, 'feynman_camera/default/rgb/image_rect_color', self.RGBimg_callback, 10
        )
        self.RGBimgLeft_subscription  # prevent unused variable warning
        
        self.leftIR_image_subscription = self.create_subscription(
            Image, 'feynman_camera/default/leftir/image_rect', self.leftIR_image_callback, 10)
        self.leftIR_image_subscription  # 防止被垃圾回收

        self.rightIR_image_subscription = self.create_subscription(
            Image, 'feynman_camera/default/rightir/image_rect', self.rightIR_image_callback, 10)
        self.rightIR_image_subscription  # 防止被垃圾回收
        
        self.DepthImgLeft_subscription = self.create_subscription(
            Image, 'feynman_camera/default/depth/image_raw', self.LeftDepthImg_callback, 10
        )
        self.DepthImgLeft_subscription

        self.DepthImgLeftRGB_subscription = self.create_subscription(
            Image, 'feynman_camera/default/depth/image_color', self.LeftDepthRGB_callback, 10
        )
        self.DepthImgLeftRGB_subscription
        

        # self.camera_info_subscription = self.create_subscription(
        #     CameraInfo, 'isaac_kepler/camera_left/camera_info', self.camera_info_callback, 10
        # )
        # self.camera_info_subscription  # prevent unused variable warning

        # self.imu_subscription = self.create_subscription(
        #     Imu, 'isaac_kepler/isaac_imu_states', self.imu_callback, 10
        # )
        # self.imu_subscription

        # 两个关节的时间和角度数据队列
        self.time_data1 = deque(maxlen=2000)
        self.time_data2 = deque(maxlen=2000)
        self.angle_data_joint1 = deque(maxlen=2000)
        self.angle_data_joint2 = deque(maxlen=2000)
        
        # 初始化实时绘图
        plt.ion()
        self.figure, self.ax = plt.subplots()
        # 创建两条不同颜色的线
        self.line_joint1, = self.ax.plot([], [], 'r-', label="Joint realtime state")  # 红色线
        self.line_joint2, = self.ax.plot([], [], 'b-', label="Joint filtered state")  # 蓝色线
        # self.ax.set_ylim(-3.14, 3.14)  # 根据关节角度范围设置Y轴
        self.ax.set_xlabel("Time")
        self.ax.set_ylabel("Joint Angle (rad)")
        self.ax.legend()  # 添加图例

        self.testnum1= 0
        self.testnum2= 0

    def timer_callback(self):
        current_time = self.get_clock().now()
        # print('Timer callback at time: ', current_time)
        delta_time = (current_time.nanoseconds - self.last_message_time.nanoseconds)*1e-9
        if  delta_time > 5.0:
            self.get_logger().info('No message received for 10 seconds. Shutting down.')
            self.close_flag = True
                            # 'data_images_LeftRGB': [],
                            # 'data_images_LeftIR': [],
                            # 'data_images_LeftDepth': [],
                            # 'data_images_LeftDepthRGB': [],
                            # 'data_images_RightIR': [],
                            # 'data_imu': [],
            print('data_clock length:',len(self.data_dict['data_clock']))
            print('data_joints length:',len(self.data_dict['data_joints']))
            print('data_action length:',len(self.data_dict['data_action']))
            print('data_images_LeftRGB length:',len(self.data_dict['data_images_LeftRGB']))
            print('data_images_LeftIR length:',len(self.data_dict['data_images_LeftIR']))
            print('data_images_LeftDepth length:',len(self.data_dict['data_images_LeftDepth']))
            print('data_images_LeftDepthRGB length:',len(self.data_dict['data_images_LeftDepthRGB']))
            print('data_images_RightIR length:',len(self.data_dict['data_images_RightIR']))
            print('data_imu length:',len(self.data_dict['data_imu']))
        
        # 可视化关节角的动态曲线
        self.ax.relim()
        self.ax.autoscale_view()
        self.figure.canvas.draw()
        self.figure.canvas.flush_events()
        # print('data_clock length:',len(self.time_data1),len(self.time_data2))

    def clock_callback(self, clock_msg):
        # rosgraph_msgs.msg.Clock(clock=builtin_interfaces.msg.Time(sec=6, nanosec=564999853))
        self.ros2_clock = clock_msg.clock.sec*1e9 + clock_msg.clock.nanosec
        self.data_dict['data_clock'].append({"time": self.ros2_clock} )
        # print('clock:',ros2_clock)

    def RGBimg_callback(self, RGB_msg):
        self.last_message_time = self.get_clock().now() # 判断当前是否有消息数据 有数据则更新时间
        # print(msg.encoding)
        RGBimg_frame =self.bridge.imgmsg_to_cv2(RGB_msg, "bgr8")   # 输出的图片是bgr,格式为uint8 输入的是msg 自动读取 msg.encoding 解析
        self.data_dict['data_clock'].append({"time": self.ros2_clock} ) # 以RGB图像的更新时间作为字典中的时间戳
        ros2_clock = RGB_msg.header.stamp.sec*1e9 + RGB_msg.header.stamp.nanosec
        self.data_dict['data_images_LeftRGB'].append({  "time": ros2_clock,
                                                        "leftRGB": RGBimg_frame}
                                                        )
        # joint_actions_callback 的频率和 state 的频率不一样 在这同步保存
        self.data_dict['data_action'].append(self.action_dict)
        self.data_dict['data_joints'].append(self.state_dict)
        cur_time = time.time()-self.start_time
        self.time_data2.append(cur_time)
        self.angle_data_joint2.append(self.state_dict['qpos'][0])
        # 更新 Joint 2 的数据
        self.line_joint2.set_xdata(list(self.time_data2))
        self.line_joint2.set_ydata(list(self.angle_data_joint2))
        
        # print('data_joints_frame,data_action_frame:',self.testnum1,self.testnum2)
        cv2.imshow("camera_left", RGBimg_frame)
        cv2.waitKey(1)
        # print('RGBimg_frame:',ros2_clock)
        self.now_time = ros2_clock
        if 'data_images_LeftRGB' in self.data_dict:
            length = len(self.data_dict['data_images_LeftRGB'])
            if self.saveImg_flag == True:
                cv2.imwrite('./image/camera_left'+str(length)+'.jpg', RGBimg_frame)
        else:
            print("'data_images_LeftRGB' not found in the dictionary")

    def leftIR_image_callback(self, leftIR_msg):
        # 使用 CvBridge 将 ROS2 图像消息转换为 OpenCV 格式
        leftIR_frame = self.bridge.imgmsg_to_cv2(leftIR_msg, desired_encoding='mono8')
        leftIR_frame = np.expand_dims(leftIR_frame, axis=-1)   # 深度图为480*640 二维数组 扩为 480*640*1 三维数组
        ros2_clock = leftIR_msg.header.stamp.sec*1e9 + leftIR_msg.header.stamp.nanosec
        self.data_dict['data_images_LeftIR'].append({  "time": ros2_clock,
                                                        "leftIR": leftIR_frame}
                                                        )
        # print('LeftIRimage shape:',leftIR_frame.shape)  # (480, 640)
        # print('LeftIRimage dtype:',leftIR_frame.dtype)    # uint8
        # print('LeftIRimage data:',leftIR_frame)
        # 显示图像 (可选)
        # print('leftIR_frame:',ros2_clock)
        cv2.imshow("Left Mono8 Image", leftIR_frame)
        cv2.waitKey(1)

    def rightIR_image_callback(self, rightIR_msg):
        # 使用 CvBridge 将 ROS2 图像消息转换为 OpenCV 格式
        rightIR_frame = self.bridge.imgmsg_to_cv2(rightIR_msg, desired_encoding='mono8')
        rightIR_frame = np.expand_dims(rightIR_frame, axis=-1)   # 深度图为480*640 二维数组 扩为 480*640*1 三维数组
        ros2_clock = rightIR_msg.header.stamp.sec*1e9 + rightIR_msg.header.stamp.nanosec
        self.data_dict['data_images_RightIR'].append({  "time": ros2_clock,
                                                        "rightIR": rightIR_frame}
                                                        )
        # print('rightIR_frame:',ros2_clock)
        # 显示图像
        cv2.imshow("right Mono8 Image", rightIR_frame)
        cv2.waitKey(1)


    def LeftDepthRGB_callback(self, LeftDepthRGB_msg):
        # print('RightRGBimage:',msg.header.stamp.sec + msg.header.stamp.nanosec/1e9)
        LeftDepthRGB_frame =self.bridge.imgmsg_to_cv2(LeftDepthRGB_msg, "bgr8")   # 输出的图片是bgr, 输入的是msg 自动读取 msg.encoding 解析
        # self.data_dict[f'/observations/images/{self.camera_names[1]}'].append(current_frame)
        ros2_clock = LeftDepthRGB_msg.header.stamp.sec*1e9 + LeftDepthRGB_msg.header.stamp.nanosec
        self.data_dict['data_images_LeftDepthRGB'].append({ "time": ros2_clock,
                                                        'leftDepthRGB': LeftDepthRGB_frame}
                                                        )
        # print('LeftDepthRGB_frame:',ros2_clock)
        cv2.imshow("camera_LeftDepthRGB", LeftDepthRGB_frame)
        cv2.waitKey(1)

    def LeftDepthImg_callback(self, LeftDepth_msg):
            # 使用CvBridge将ROS的Image消息转换为OpenCV图像
            LeftDepthImg_frame = self.bridge.imgmsg_to_cv2(LeftDepth_msg, desired_encoding='passthrough')   # 16UC1
            # 这里你可以处理深度图像数据，例如将其显示或保存
            # print(f"Image shape: {cv_image.shape}") # Image shape: (480, 640)
            # print(f"Image dtype: {cv_image.dtype}") # feynman_camera 相机是 16UC1 ，isaac sim 中输出的深度图是 32FC1 32 位浮点单通道
            # print(cv_image)
            # 转换为 float16 图像
            # image_float16 = cv_image.astype(np.float16)
            # depth_image_16uc1 = np.clip(image_float16*1000, 0, 65535).astype(np.uint16)
            expanded_depth_image = np.expand_dims(LeftDepthImg_frame, axis=-1)   # 深度图为480*640 二维数组 扩为 480*640*1 三维数组
            # depth_image_rgb_stack = np.stack((depth_image_16uc1,)*3, axis=-1)
            # print(f"depth_image_16uc3 shape: {depth_image_rgb_stack.shape}")
            # print(f"depth_image_16uc3 dtype: {depth_image_rgb_stack.dtype}")
            # print(depth_image_rgb_stack)
            ros2_clock = LeftDepth_msg.header.stamp.sec*1e9 + LeftDepth_msg.header.stamp.nanosec
            self.data_dict['data_images_LeftDepth'].append({  "time": ros2_clock,
                                                            "leftDepth": expanded_depth_image}
                                                            )
            # print('LeftDepthImg_frame:',ros2_clock)
            cv2.imshow("Depth Image", LeftDepthImg_frame)
            cv2.waitKey(1)  # 1ms 的延迟，让OpenCV可以显示图像
            if 'data_images_LeftDepth' in self.data_dict:
                length = len(self.data_dict['data_images_LeftDepth'])
                if self.saveImg_flag==True:
                    print(f"Depth image dtype: {LeftDepthImg_frame.dtype}")
                    # 归一化深度图到 0-255 范围，以便保存
                    depth_normalized = cv2.normalize(LeftDepthImg_frame, None, 0, 255, cv2.NORM_MINMAX)
                    # 将深度图转换为 8 位图像（0-255）
                    depth_image_8bit = depth_normalized.astype(np.uint8)
                    # 保存为可视化的深度图（PNG）
                    cv2.imwrite('./image/camera_left_depth'+str(length)+'.png', depth_image_8bit)
            else:
                print("'data_images_LeftDepth' not found in the dictionary")

    def joint_state_callback(self, joint_msg):
        # self.get_logger().info('I heard: "%s"' % msg)   # 关节角名称 msg.name 关节角rad数组 msg.position velocity effort
        ros2_clock = joint_msg.header.stamp.sec*1e9 + joint_msg.header.stamp.nanosec
        self.state_dict ={  "time": ros2_clock,
                            "name": joint_msg.name,
                            "qpos": joint_msg.position,
                            "qvel": joint_msg.velocity,
                            "effort": joint_msg.effort}
        self.testnum1+=1
        cur_time = time.time()-self.start_time
        # 添加数据到队列
        self.time_data1.append(cur_time)
        self.angle_data_joint1.append(self.state_dict['qpos'][0])
        self.line_joint1.set_xdata(list(self.time_data1))
        self.line_joint1.set_ydata(list(self.angle_data_joint1))

        # print('self.time_data',self.time_data1)
        # self.data_dict['data_joints'].append(self.state_dict)
        # print('joint_state:',ros2_clock)
    
    def joint_actions_callback(self, msg):
        self.ros2_clock = msg.header.stamp.sec*1e9 + msg.header.stamp.nanosec   # 120Hz 以高频率的时间戳为全局系统时间
        self.action_dict = {  "time": self.ros2_clock,    # 使用接收到的ros2时间戳为动作时间
                                "name": msg.name,
                                "qpos": msg.position,
                                "qvel": msg.velocity,
                                "effort": msg.effort}
        self.testnum2+=1
        # self.data_dict['data_action'].append(self.action_dict)   
        # print('joint_actions:',self.ros2_clock)

    def imu_callback(self, msg):
        ros2_clock = msg.header.stamp.sec*1e9 + msg.header.stamp.nanosec
        self.data_dict['data_imu'].append({"time": ros2_clock,
            "linear_acceleration": [msg.linear_acceleration.x,msg.linear_acceleration.y,msg.linear_acceleration.z],
            "angular_velocity": [msg.angular_velocity.x, msg.angular_velocity.y, msg.angular_velocity.z],
            "orientation": [msg.orientation.x, msg.orientation.y, msg.orientation.z, msg.orientation.w]}
        )
        # print('imu:',ros2_clock)

    def close_figure(self):
        # 关闭画幅
        plt.ioff()  # 关闭交互模式
        plt.show(block=False)   # 非阻塞模式显示图像窗口，后续代码会继续执行
        # 延迟关闭窗口
        time.sleep(0.1)  # 显示图像 3 秒
        plt.close()  # 关闭窗口

def dict_align_timestamp(data_dict_initial):
    # data_dict = data_dict_initial
    # 删除空列表的键
    data_dict = {key: value for key, value in data_dict_initial.items() if value}
    # for key, value in data_dict.items():    # 删除比最大time值小的元素
    #     for n in range(len(value)):
    #         if value[0]['time'] < max_value:
    #             data_dict[key].pop(0) 
    #         if value[0]['time'] == max_value and value[1]['time']==max_value:
    #             data_dict[key].pop(0)
    #         if value[0]['time'] == max_value and value[1]['time'] > max_value:
    #             print(key,'初始时间戳:',max_value)
    #             break
    

    # 遍历 self.data_dict 的每个键，找到第一个 time 为 0 的情况
    for key, data_list in data_dict.items():
        # 如果当前列表非空并且第一个元素的 time 为 0
        if data_list and data_list[0]['time'] == 0:
            # 遍历所有键并执行弹出操作
            for key in data_dict:
                if data_dict[key]:  # 确保列表非空
                    data_dict[key].pop(0)

    # key_name,key_time=[],[]
    # img_key_name,img_start_time,img_end_time = [],[],[]
    # for key, value in data_dict.items():
    #     #print(key, value)
    #     if len(value) != 0 :
    #         # key_name.append(key)
    #         # key_time.append(value[0]['time'])
    #         if key.startswith('data_images'):
    #             # img_key_name.append(key)
    #             # img_start_time.append(value[0]['time'])
    #             # img_end_time.append(value[-1]['time'])
    #     else:
    #         del data_dict[key]  # 去除空的集合
    
    # 找到长度最小的列表及其时间范围
    min_key = min(data_dict, key=lambda k: len(data_dict[k]))
    min_list = data_dict[min_key]
    min_length = len(min_list)

    # 裁剪image的列表
    for key, data_list in data_dict.items():
        #if key.startswith('data_images'):   # 判断是不是image list
            while len(data_list) > min_length : # 裁剪尾部直到结束时间满足要求
                    data_list.pop()
    
    # # 查找最临近的时间
    # for key, value in data_dict.items():    # 删除比最大time值小的元素 data_dict['data_images_LeftRGB'][0]['leftRGB'] time
    #     last_index = -1
    #     closest_index_data = []
    #     if not key.startswith('data_images'):   # 对齐非image list 相机的数据已对齐
    #         for refer in data_dict['data_images_LeftRGB']:
    #             closest_value = None
    #             min_diff = float('inf')
    #             for i in range(last_index + 1, len(value)):
    #                 diff = abs(value[i]['time'] - refer['time'])
    #                 if diff < min_diff:
    #                     min_diff = diff
    #                     closest_value = value[i]['time']
    #                     last_index = i
    #             closest_index_data.append(value[i])
    #             # print(f"Closest value for {key} at {refer['time']} is {closest_value} at index {last_index}")
    #         data_dict[key] = closest_index_data





    # # 找到非空列表的最小长度
    # min_length = None
    # for lst in data_dict.values():
    #     if lst:  # 检查列表是否为空
    #         if min_length is None or len(lst) < min_length:
    #             min_length = len(lst)
    # #print(data_dict.values())
    # # 遍历每个列表，删除长度大于最小值的元素
    # for key, value in data_dict.items():
    #     if value:  # 检查列表是否为空
    #         while len(value) > min_length:
    #             value.pop()
    #         print(key,'结束时间戳:',data_dict[key][-1]['time'],'长度:',len(data_dict[key]))

    return data_dict

def dict_Original_to_Standard(data_dict):   # 保存需要的数据 主要是 jointstates 和 actions 的数据筛选
    """ input:
        self.data_dict = { 'data_clock': [],
                            'data_joints': [],
                            'data_action': [],
                            'data_images_LeftRGB': [],
                            'data_images_LeftIR': [],
                            'data_images_LeftDepth': [],
                            'data_images_LeftDepthRGB': [],
                            'data_images_RightIR': [],
                            'data_imu': [],
        }
        output:
        For each timestep:
        observations
        - images
            - cam_high          (480, 640, 3) 'uint8'
            - cam_low           (480, 640, 3) 'uint8'
            - cam_left_wrist    (480, 640, 3) 'uint8'
            - cam_right_wrist   (480, 640, 3) 'uint8'
        - qpos                  (14,)         'float64'
        - qvel                  (14,)         'float64'
        
        action                  (14,)         'float64'
        """
    qpos = [item['qpos'] for item in data_dict['data_joints']]
    qvel = [item.get('qvel', 0) for item in data_dict['data_joints']]
    effort = [item['effort'] for item in data_dict['data_joints']]
    action = [item['qpos'] for item in data_dict['data_action']]
    camera_leftRGB = [item['leftRGB'] for item in data_dict['data_images_LeftRGB']]
    camera_leftIR = [item['leftIR'] for item in data_dict['data_images_LeftIR']]
    camera_rightIR = [item['rightIR'] for item in data_dict['data_images_RightIR']]
    camera_leftDepth = [item['leftDepth'] for item in data_dict['data_images_LeftDepth']]
    camera_leftDepthRGB = [item['leftDepthRGB'] for item in data_dict['data_images_LeftDepthRGB']]
    # imulin = [item['linear_acceleration'] for item in data_dict['data_imu']]
    # 注意! 不同的关节名需要重新定义!
    requir_joint_name =['J31', 'J32', 'J33', 'J34', 'J35', 'J36', 'J37', 'J41', 'J42', 'J43', 'J44', 'J45', 'J46', 'J47',
                        'J56', 'J51', 'J52', 'J53', 'J54', 'J55',
                        'J66', 'J61', 'J62', 'J63', 'J64', 'J65',
                        'J90','J91','Jhead_yaw','Jhead_pitch']
    # requir_joint_name =['J31', 'J32', 'J33', 'J34', 'J35', 'J36', 'J37', 'J41', 'J42', 'J43', 'J44', 'J45', 'J46', 'J47']
    # print('data_dict: data_joints',data_dict['data_joints'][0]['name'])
    print('requir_joint_name:',requir_joint_name)
    data_joints_name= data_dict['data_joints'][0]['name']
    indices = [data_joints_name.index(name) for name in requir_joint_name]
    qpos_need = np.array(qpos)[:, indices]
    qvel_need = np.array(qvel)[:, indices]#[qvel[index] for index in indices]
    effort_need = np.array(effort)[:, indices]#[effort[index] for index in indices]
    
    data_action_name= data_dict['data_action'][0]['name']
    indices = [data_action_name.index(name) for name in requir_joint_name]
    action_need = np.array(action)[:, indices]# [action[index] for index in indices]
    #print('qpos',qpos[100])
    print('data_joints_name',data_joints_name)
    #print('qpos_need',qpos_need[100])

    #print('action',action[100])
    print('data_action_name',data_action_name)
    #print('action_need',action_need[100])
    new_data_dict = {
        '/observations/qpos': qpos_need,
        '/observations/qvel': qvel_need,
        '/observations/effort': effort_need,
        '/action': action_need,
        '/observations/images/camera_left': camera_leftRGB,
        # '/observations/images/camera_right': camera_rightRGB,
        '/observations/images/camera_left_depth': camera_leftDepth,
        '/observations/images/camera_leftIR': camera_leftIR,
        '/observations/images/camera_rightIR': camera_rightIR,
        '/observations/images/camera_leftDepthRGB': camera_leftDepthRGB,
    }

    return new_data_dict,len(qpos)

def get_auto_index(dataset_dir, dataset_name_prefix = '', data_suffix = 'hdf5'):    # 检测是否存在同名文件
    max_idx = 1000
    if not os.path.isdir(dataset_dir):
        os.makedirs(dataset_dir)
    for i in range(max_idx+1):
        if not os.path.isfile(os.path.join(dataset_dir, f'{dataset_name_prefix}episode_{i}.{data_suffix}')):
            return i
    raise Exception(f"Error getting auto index, or more than {max_idx} episodes")

def main(folder_name):
    rclpy.init(args=None)  # 初始化rclpy
    # node = Node("sub_isaacsim_node")  # 新建一个节点
    # node.get_logger().info("大家好,sub_isaacsim_node.")
    # rclpy.spin(node) # 保持节点运行，检测是否收到退出指令（Ctrl+C）
    topic_demo_subscriber = TopicDemoSubscriber()  # 创建订阅者实例
    topic_demo_subscriber.get_logger().info("大家好,sub_isaacsim_node.")
    #rclpy.spin(topic_demo_subscriber)  # 循环
    while rclpy.ok() and topic_demo_subscriber.close_flag != True:
        rclpy.spin_once(topic_demo_subscriber)
        if not rclpy.ok():
            break
    
    data_dict = topic_demo_subscriber.data_dict

    topic_demo_subscriber.close_figure() # 关闭画幅 无效
    topic_demo_subscriber.destroy_node()
    rclpy.shutdown()
    print("sub_isaacsim_node is shutdown")
    cv2.imwrite('camera_left.jpg', data_dict['data_images_LeftRGB'][0]['leftRGB'])

    data_dict = dict_align_timestamp(data_dict)
    data_dict, max_timesteps= dict_Original_to_Standard(data_dict)

    # 数据集保存
    DATA_DIR = '.'
    TASK_CONFIGS = {
        'task1':{
            'dataset_dir': DATA_DIR + '/HDF5',
            'num_episodes': 50,
            'episode_len': 800,
            'camera_names': ["camera_left", "camera_right"]
        },
    }
    DT = 0.05   # delta time
    task_config = TASK_CONFIGS['task1']
    dataset_dir = task_config['dataset_dir']    # data save dir
    camera_names = task_config['camera_names']

    if folder_name is not None: # episode_idx 数据次数
        episode_idx = folder_name
    else:
        episode_idx = get_auto_index(dataset_dir)
    
    # saving dataset
    overwrite = True
    # dataset_name = f'episode_{episode_idx}'
    dataset_name = episode_idx
    # print('dataset_name:',dataset_name + '\n')
    if not os.path.isdir(dataset_dir):
        os.makedirs(dataset_dir)
    dataset_path = os.path.join(dataset_dir, dataset_name)
    if os.path.isfile(dataset_path) and not overwrite:
        print(f'Dataset already exist at \n{dataset_path}\nHint: set overwrite to True.')
        exit()

    # HDF5
    t0 = time.time()

    dual_arm = 14
    dual_arm = 14+6*2+4  # 双臂14dof + 灵巧手 6*2 dof + 4 dof 腰部2和头部2
    high = 480
    with h5py.File(dataset_path + '.hdf5', 'w', rdcc_nbytes=1024**2*2) as root:
        root.attrs['sim'] = False
        obs = root.create_group('observations')
        image = obs.create_group('images')
        # for cam_name in camera_names:
        #     _ = image.create_dataset(cam_name, (max_timesteps, 480, 640, 3), dtype='uint8',
        #                              chunks=(1, 480, 640, 3), )
        _ = image.create_dataset('camera_left', (max_timesteps, high, 640, 3), dtype='uint8',
                                      chunks=(1, high, 640, 3), )
        # depth data
        _ = image.create_dataset('camera_left_depth', (max_timesteps, high, 640, 1), dtype='uint16',
                                      chunks=(1, high, 640, 1), )
        _ = image.create_dataset('camera_leftIR', (max_timesteps, high, 640, 1), dtype='uint8',
                                      chunks=(1, high, 640, 1), )
        _ = image.create_dataset('camera_rightIR', (max_timesteps, high, 640, 1), dtype='uint8',
                                      chunks=(1, high, 640, 1), )
        _ = image.create_dataset('camera_leftDepthRGB', (max_timesteps, high, 640, 3), dtype='uint8',
                                      chunks=(1, high, 640, 3), )
            # compression='gzip',compression_opts=2,)
            # compression=32001, compression_opts=(0, 0, 0, 0, 9, 1, 1), shuffle=False)
        _ = obs.create_dataset('qpos', (max_timesteps, dual_arm))
        _ = obs.create_dataset('qvel', (max_timesteps, dual_arm))
        _ = obs.create_dataset('effort', (max_timesteps, dual_arm))
        _ = root.create_dataset('action', (max_timesteps, dual_arm))

        for name, array in data_dict.items():
            root[name][...] = array
    print(f'Saving: {time.time() - t0:.1f} secs')
    print(f'Saved file:',dataset_path + '.hdf5' + '\n')


import threading
import subprocess

def ros2bag_play_thread(ros2bag_name):
    time.sleep(2)
    print('ros2bag play thread started')
    process = subprocess.Popen(["ros2", "bag", "play",ros2bag_name])
    process.wait()

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument('--task_name', action='store', type=str, help='Task name.',default='task1',required=False)
    parser.add_argument('--episode_idx', action='store', type=int, help='Episode index.', default=100, required=False)
    # 指定父文件夹路径
    parent_dir = "/home/<USER>/dataset1029/record/ros2bags"
    # # 获取所有子文件夹
    subfolders = [f for f in os.listdir(parent_dir) if os.path.isdir(os.path.join(parent_dir, f))]
    # subfolders = []
    # # 循环生成数字并添加到列表中
    # for i in range(1, 9):
    #     subfolders.append('teleoperationGrip00' + str(i))
    # subfolders = ['teleoperation1032_2024-10-29-03-06-57']
    # 循环读取每一个子文件夹
    for index, folder_name in enumerate(subfolders):
        # 判断前缀是否为 'ros2'
        if folder_name.startswith('teleoperation'):
            print(f"file: {folder_name}")
            # 创建并启动ros2 bag play线程
            ros2_thread = threading.Thread(target=ros2bag_play_thread,args=(folder_name,))
            ros2_thread.start()
            main(folder_name)
        else:
            print(f"{folder_name} does not start with 'ros2'")


