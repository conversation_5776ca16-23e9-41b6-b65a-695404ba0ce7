#!/usr/bin/env python3
"""
HDF5 数据可视化脚本
用于可视化机器人数据集中的图像、关节位置和动作数据

Usage:
    python visualize_hdf5.py --data_dir /home/<USER>/act-plus-plus/datasets/ym_touch
    python visualize_hdf5.py --file /path/to/specific.hdf5
"""

import os
import argparse
import h5py
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.animation as animation
from matplotlib.widgets import Slider, Button
import cv2

class HDF5Visualizer:
    def __init__(self, hdf5_path):
        self.hdf5_path = hdf5_path
        self.data = None
        self.current_frame = 0
        self.total_frames = 0
        self.playing = False
        
        # 加载数据
        self.load_data()
        
        # 设置图形界面
        self.setup_gui()
        
    def load_data(self):
        """加载HDF5数据"""
        print(f"Loading data from {self.hdf5_path}")
        with h5py.File(self.hdf5_path, 'r') as f:
            # 打印文件结构
            print("HDF5 file structure:")
            self.print_structure(f)
            
            # 加载数据
            self.data = {}
            
            # 加载图像数据
            if 'observations/images' in f:
                img_group = f['observations/images']
                for cam_name in img_group.keys():
                    self.data[f'images_{cam_name}'] = img_group[cam_name][:]
                    print(f"Loaded {cam_name} images: {img_group[cam_name].shape}")
            
            # 加载关节位置
            if 'observations/qpos' in f:
                self.data['qpos'] = f['observations/qpos'][:]
                print(f"Loaded qpos: {self.data['qpos'].shape}")
            
            # 加载关节速度
            if 'observations/qvel' in f:
                self.data['qvel'] = f['observations/qvel'][:]
                print(f"Loaded qvel: {self.data['qvel'].shape}")
            
            # 加载动作数据
            if 'action' in f:
                self.data['action'] = f['action'][:]
                print(f"Loaded action: {self.data['action'].shape}")
        
        # 确定总帧数
        if 'qpos' in self.data:
            self.total_frames = len(self.data['qpos'])
        elif any(key.startswith('images_') for key in self.data.keys()):
            img_key = next(key for key in self.data.keys() if key.startswith('images_'))
            self.total_frames = len(self.data[img_key])
        
        print(f"Total frames: {self.total_frames}")
    
    def print_structure(self, group, indent=""):
        """递归打印HDF5文件结构"""
        for key in group.keys():
            item = group[key]
            if isinstance(item, h5py.Dataset):
                print(f"{indent}{key}: {item.shape} {item.dtype}")
            elif isinstance(item, h5py.Group):
                print(f"{indent}{key}/")
                self.print_structure(item, indent + "  ")
    
    def setup_gui(self):
        """设置GUI界面"""
        # 计算子图布局
        n_images = sum(1 for key in self.data.keys() if key.startswith('images_'))
        has_joints = 'qpos' in self.data
        has_actions = 'action' in self.data
        
        # 创建图形窗口
        if n_images > 0:
            fig_width = 15
            fig_height = 10
        else:
            fig_width = 12
            fig_height = 8
            
        self.fig = plt.figure(figsize=(fig_width, fig_height))
        self.fig.suptitle(f'HDF5 Data Visualization: {os.path.basename(self.hdf5_path)}')
        
        # 创建子图
        self.axes = {}
        
        if n_images > 0:
            # 图像显示区域
            for i, key in enumerate([k for k in self.data.keys() if k.startswith('images_')]):
                cam_name = key.replace('images_', '')
                ax = self.fig.add_subplot(2, max(n_images, 2), i + 1)
                ax.set_title(f'{cam_name.title()} Camera')
                ax.axis('off')
                self.axes[f'img_{cam_name}'] = ax
        
        # 关节位置图
        if has_joints:
            ax_joints = self.fig.add_subplot(2, 2, 3)
            ax_joints.set_title('Joint Positions')
            ax_joints.set_xlabel('Joint Index')
            ax_joints.set_ylabel('Position')
            self.axes['joints'] = ax_joints
        
        # 动作图
        if has_actions:
            ax_actions = self.fig.add_subplot(2, 2, 4)
            ax_actions.set_title('Actions')
            ax_actions.set_xlabel('Action Index')
            ax_actions.set_ylabel('Value')
            self.axes['actions'] = ax_actions
        
        # 添加控制组件
        self.add_controls()
        
        # 初始化显示
        self.update_display()
    
    def add_controls(self):
        """添加控制按钮和滑块"""
        # 滑块区域
        ax_slider = plt.axes([0.1, 0.02, 0.6, 0.03])
        self.slider = Slider(ax_slider, 'Frame', 0, max(1, self.total_frames-1), 
                           valinit=0, valfmt='%d')
        self.slider.on_changed(self.on_slider_change)
        
        # 播放/暂停按钮
        ax_play = plt.axes([0.75, 0.02, 0.08, 0.04])
        self.btn_play = Button(ax_play, 'Play')
        self.btn_play.on_clicked(self.toggle_play)
        
        # 重置按钮
        ax_reset = plt.axes([0.85, 0.02, 0.08, 0.04])
        self.btn_reset = Button(ax_reset, 'Reset')
        self.btn_reset.on_clicked(self.reset_view)
    
    def on_slider_change(self, val):
        """滑块变化回调"""
        self.current_frame = int(val)
        self.update_display()
    
    def toggle_play(self, event):
        """播放/暂停切换"""
        self.playing = not self.playing
        self.btn_play.label.set_text('Pause' if self.playing else 'Play')
        if self.playing:
            self.animate()
    
    def reset_view(self, event):
        """重置视图"""
        self.current_frame = 0
        self.playing = False
        self.btn_play.label.set_text('Play')
        self.slider.reset()
        self.update_display()
    
    def animate(self):
        """动画播放"""
        if self.playing and self.current_frame < self.total_frames - 1:
            self.current_frame += 1
            self.slider.set_val(self.current_frame)
            self.update_display()
            self.fig.canvas.draw_idle()
            # 设置定时器继续播放
            self.fig.canvas.start_event_loop(0.1)
            if self.playing:  # 检查是否仍在播放
                self.animate()
        else:
            self.playing = False
            self.btn_play.label.set_text('Play')
    
    def update_display(self):
        """更新显示内容"""
        if self.current_frame >= self.total_frames:
            return
            
        # 更新图像
        for key in self.data.keys():
            if key.startswith('images_'):
                cam_name = key.replace('images_', '')
                ax_key = f'img_{cam_name}'
                if ax_key in self.axes:
                    ax = self.axes[ax_key]
                    ax.clear()
                    ax.set_title(f'{cam_name.title()} Camera - Frame {self.current_frame}')
                    
                    img = self.data[key][self.current_frame]
                    # 转换BGR到RGB (如果需要)
                    if len(img.shape) == 3 and img.shape[2] == 3:
                        img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
                    ax.imshow(img)
                    ax.axis('off')
        
        # 更新关节位置
        if 'joints' in self.axes and 'qpos' in self.data:
            ax = self.axes['joints']
            ax.clear()
            ax.set_title(f'Joint Positions - Frame {self.current_frame}')
            ax.set_xlabel('Joint Index')
            ax.set_ylabel('Position')
            
            qpos = self.data['qpos'][self.current_frame]
            ax.bar(range(len(qpos)), qpos)
            ax.grid(True, alpha=0.3)
        
        # 更新动作
        if 'actions' in self.axes and 'action' in self.data:
            ax = self.axes['actions']
            ax.clear()
            ax.set_title(f'Actions - Frame {self.current_frame}')
            ax.set_xlabel('Action Index')
            ax.set_ylabel('Value')
            
            action = self.data['action'][self.current_frame]
            ax.bar(range(len(action)), action)
            ax.grid(True, alpha=0.3)
        
        self.fig.canvas.draw_idle()
    
    def show(self):
        """显示可视化界面"""
        plt.tight_layout()
        plt.show()

def main():
    parser = argparse.ArgumentParser(description='Visualize HDF5 robot dataset')
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument('--file', type=str, help='Path to specific HDF5 file')
    group.add_argument('--data_dir', type=str, help='Directory containing HDF5 files')
    
    args = parser.parse_args()
    
    if args.file:
        if not os.path.exists(args.file):
            print(f"Error: File {args.file} not found")
            return
        visualizer = HDF5Visualizer(args.file)
        visualizer.show()
    
    elif args.data_dir:
        if not os.path.exists(args.data_dir):
            print(f"Error: Directory {args.data_dir} not found")
            return
        
        # 列出所有HDF5文件
        hdf5_files = [f for f in os.listdir(args.data_dir) if f.endswith('.hdf5')]
        if not hdf5_files:
            print(f"No HDF5 files found in {args.data_dir}")
            return
        
        hdf5_files.sort()
        print(f"Found {len(hdf5_files)} HDF5 files:")
        for i, f in enumerate(hdf5_files):
            print(f"  {i}: {f}")
        
        # 让用户选择文件
        try:
            choice = input(f"Enter file number (0-{len(hdf5_files)-1}) or press Enter for first file: ")
            if choice.strip() == "":
                choice = 0
            else:
                choice = int(choice)
            
            if 0 <= choice < len(hdf5_files):
                file_path = os.path.join(args.data_dir, hdf5_files[choice])
                visualizer = HDF5Visualizer(file_path)
                visualizer.show()
            else:
                print("Invalid choice")
        except (ValueError, KeyboardInterrupt):
            print("Cancelled")

if __name__ == '__main__':
    main()
