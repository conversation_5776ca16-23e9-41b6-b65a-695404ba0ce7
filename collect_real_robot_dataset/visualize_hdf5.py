#!/usr/bin/env python3
"""
HDF5 数据可视化脚本
用于可视化机器人数据集中的图像、关节位置和动作数据

Usage:
    python visualize_hdf5.py --data_dir /home/<USER>/act-plus-plus/datasets/ym_touch
    python visualize_hdf5.py --file /path/to/specific.hdf5
"""

import os
import argparse
import h5py
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.animation as animation
from matplotlib.widgets import Slider, Button
import cv2

class HDF5Visualizer:
    def __init__(self, hdf5_path):
        self.hdf5_path = hdf5_path
        self.data = None
        self.current_frame = 0
        self.total_frames = 0
        self.playing = False
        
        # 加载数据
        self.load_data()
        
        # 设置图形界面
        self.setup_gui()
        
    def load_data(self):
        """加载HDF5数据"""
        print(f"Loading data from {self.hdf5_path}")
        with h5py.File(self.hdf5_path, 'r') as f:
            # 打印文件结构
            print("HDF5 file structure:")
            self.print_structure(f)
            
            # 加载数据
            self.data = {}
            
            # 加载图像数据
            if 'observations/images' in f:
                img_group = f['observations/images']
                for cam_name in img_group.keys():
                    self.data[f'images_{cam_name}'] = img_group[cam_name][:]
                    print(f"Loaded {cam_name} images: {img_group[cam_name].shape}")
            
            # 加载关节位置
            if 'observations/qpos' in f:
                self.data['qpos'] = f['observations/qpos'][:]
                print(f"Loaded qpos: {self.data['qpos'].shape}")
            
            # 加载关节速度
            if 'observations/qvel' in f:
                self.data['qvel'] = f['observations/qvel'][:]
                print(f"Loaded qvel: {self.data['qvel'].shape}")
            
            # 加载动作数据
            if 'action' in f:
                self.data['action'] = f['action'][:]
                print(f"Loaded action: {self.data['action'].shape}")
        
        # 确定总帧数
        if 'qpos' in self.data:
            self.total_frames = len(self.data['qpos'])
        elif any(key.startswith('images_') for key in self.data.keys()):
            img_key = next(key for key in self.data.keys() if key.startswith('images_'))
            self.total_frames = len(self.data[img_key])
        
        print(f"Total frames: {self.total_frames}")
    
    def print_structure(self, group, indent=""):
        """递归打印HDF5文件结构"""
        for key in group.keys():
            item = group[key]
            if isinstance(item, h5py.Dataset):
                print(f"{indent}{key}: {item.shape} {item.dtype}")
            elif isinstance(item, h5py.Group):
                print(f"{indent}{key}/")
                self.print_structure(item, indent + "  ")
    
    def setup_gui(self):
        """设置GUI界面"""
        # 计算子图布局
        n_images = sum(1 for key in self.data.keys() if key.startswith('images_'))
        has_joints = 'qpos' in self.data
        has_actions = 'action' in self.data

        # 创建图形窗口 - 更大的窗口以容纳更多内容
        fig_width = 20
        fig_height = 12

        self.fig = plt.figure(figsize=(fig_width, fig_height))
        self.fig.suptitle(f'HDF5 Data Visualization: {os.path.basename(self.hdf5_path)}',
                         fontsize=16, fontweight='bold')

        # 设置整体样式
        plt.style.use('seaborn-v0_8' if 'seaborn-v0_8' in plt.style.available else 'default')

        # 创建子图
        self.axes = {}

        if n_images > 0:
            # 图像显示区域 - 上半部分
            for i, key in enumerate([k for k in self.data.keys() if k.startswith('images_')]):
                cam_name = key.replace('images_', '')
                ax = self.fig.add_subplot(3, max(n_images, 3), i + 1)
                ax.set_title(f'{cam_name.title()} Camera', fontsize=12, fontweight='bold')
                ax.axis('off')
                # 添加边框
                for spine in ax.spines.values():
                    spine.set_visible(True)
                    spine.set_linewidth(2)
                    spine.set_edgecolor('gray')
                self.axes[f'img_{cam_name}'] = ax

        # 关节位置时间序列图 - 中间部分
        if has_joints:
            ax_joints = self.fig.add_subplot(3, 1, 2)
            ax_joints.set_title('Joint Positions Over Time', fontsize=12, fontweight='bold')
            ax_joints.set_xlabel('Frame', fontsize=10)
            ax_joints.set_ylabel('Position (rad/m)', fontsize=10)
            ax_joints.grid(True, alpha=0.3)
            ax_joints.set_facecolor('#f8f9fa')
            self.axes['joints'] = ax_joints

        # 动作时间序列图 - 下半部分
        if has_actions:
            ax_actions = self.fig.add_subplot(3, 1, 3)
            ax_actions.set_title('Actions Over Time', fontsize=12, fontweight='bold')
            ax_actions.set_xlabel('Frame', fontsize=10)
            ax_actions.set_ylabel('Action Value', fontsize=10)
            ax_actions.grid(True, alpha=0.3)
            ax_actions.set_facecolor('#f8f9fa')
            self.axes['actions'] = ax_actions

        # 预计算时间序列数据用于绘图
        self.prepare_time_series_data()

        # 添加控制组件
        self.add_controls()

        # 初始化显示
        self.update_display()

    def prepare_time_series_data(self):
        """预计算时间序列数据，用于高效绘图"""
        self.time_series = {}

        # 准备关节数据
        if 'qpos' in self.data:
            qpos_data = self.data['qpos']
            self.time_series['qpos'] = qpos_data
            # 创建关节名称
            n_joints = qpos_data.shape[1]
            if n_joints >= 14:  # 假设前14个是手臂关节
                self.joint_names = ([f'L_Arm_{i+1}' for i in range(7)] +
                                  [f'R_Arm_{i+1}' for i in range(7)] +
                                  [f'Gripper_{i+1}' for i in range(n_joints-14)])
            else:
                self.joint_names = [f'Joint_{i+1}' for i in range(n_joints)]

        # 准备动作数据
        if 'action' in self.data:
            action_data = self.data['action']
            self.time_series['action'] = action_data
            # 创建动作名称
            n_actions = action_data.shape[1]
            if n_actions >= 16:  # 假设是双臂+手的配置
                self.action_names = ([f'L_Arm_{i+1}' for i in range(7)] +
                                   [f'R_Arm_{i+1}' for i in range(7)] +
                                   ['L_Hand', 'R_Hand'] +
                                   [f'Extra_{i+1}' for i in range(n_actions-16)])
            else:
                self.action_names = [f'Action_{i+1}' for i in range(n_actions)]

        # 创建颜色映射
        self.colors = plt.cm.tab20(np.linspace(0, 1, 20))  # 20种不同颜色

    def add_controls(self):
        """添加控制按钮和滑块"""
        # 调整布局为控制组件留出空间
        plt.subplots_adjust(bottom=0.15)

        # 滑块区域 - 更大更美观
        ax_slider = plt.axes([0.1, 0.05, 0.6, 0.04])
        ax_slider.set_facecolor('#e9ecef')
        self.slider = Slider(ax_slider, 'Frame', 0, max(1, self.total_frames-1),
                           valinit=0, valfmt='%d',
                           facecolor='#007bff', alpha=0.8)
        self.slider.on_changed(self.on_slider_change)

        # 播放/暂停按钮 - 美化样式
        ax_play = plt.axes([0.72, 0.05, 0.08, 0.04])
        self.btn_play = Button(ax_play, '▶ Play', color='#28a745', hovercolor='#218838')
        self.btn_play.label.set_fontsize(10)
        self.btn_play.label.set_fontweight('bold')
        self.btn_play.on_clicked(self.toggle_play)

        # 重置按钮 - 美化样式
        ax_reset = plt.axes([0.82, 0.05, 0.08, 0.04])
        self.btn_reset = Button(ax_reset, '⟲ Reset', color='#6c757d', hovercolor='#5a6268')
        self.btn_reset.label.set_fontsize(10)
        self.btn_reset.label.set_fontweight('bold')
        self.btn_reset.on_clicked(self.reset_view)

        # 添加帧信息文本
        ax_info = plt.axes([0.1, 0.01, 0.8, 0.03])
        ax_info.axis('off')
        self.info_text = ax_info.text(0.5, 0.5, f'Frame: 0 / {self.total_frames-1}',
                                     ha='center', va='center', fontsize=12,
                                     fontweight='bold', transform=ax_info.transAxes)
    
    def on_slider_change(self, val):
        """滑块变化回调"""
        self.current_frame = int(val)
        self.update_info_text()
        self.update_display()

    def toggle_play(self, _):
        """播放/暂停切换"""
        self.playing = not self.playing
        if self.playing:
            self.btn_play.label.set_text('⏸ Pause')
            self.btn_play.color = '#dc3545'
            self.btn_play.hovercolor = '#c82333'
            self.animate()
        else:
            self.btn_play.label.set_text('▶ Play')
            self.btn_play.color = '#28a745'
            self.btn_play.hovercolor = '#218838'

    def reset_view(self, _):
        """重置视图"""
        self.current_frame = 0
        self.playing = False
        self.btn_play.label.set_text('▶ Play')
        self.btn_play.color = '#28a745'
        self.btn_play.hovercolor = '#218838'
        self.slider.reset()
        self.update_info_text()
        self.update_display()

    def update_info_text(self):
        """更新信息文本"""
        if hasattr(self, 'info_text'):
            self.info_text.set_text(f'Frame: {self.current_frame} / {self.total_frames-1} '
                                   f'({self.current_frame/max(1, self.total_frames-1)*100:.1f}%)')
    
    def animate(self):
        """动画播放"""
        if self.playing and self.current_frame < self.total_frames - 1:
            self.current_frame += 1
            self.slider.set_val(self.current_frame)
            self.update_display()
            self.fig.canvas.draw_idle()
            # 设置定时器继续播放
            self.fig.canvas.start_event_loop(0.1)
            if self.playing:  # 检查是否仍在播放
                self.animate()
        else:
            self.playing = False
            self.btn_play.label.set_text('Play')
    
    def update_display(self):
        """更新显示内容"""
        if self.current_frame >= self.total_frames:
            return

        # 更新图像
        for key in self.data.keys():
            if key.startswith('images_'):
                cam_name = key.replace('images_', '')
                ax_key = f'img_{cam_name}'
                if ax_key in self.axes:
                    ax = self.axes[ax_key]
                    ax.clear()
                    ax.set_title(f'{cam_name.title()} Camera - Frame {self.current_frame}',
                               fontsize=12, fontweight='bold')

                    img = self.data[key][self.current_frame]
                    # 转换BGR到RGB (如果需要)
                    if len(img.shape) == 3 and img.shape[2] == 3:
                        img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
                    ax.imshow(img)
                    ax.axis('off')

                    # 添加边框
                    for spine in ax.spines.values():
                        spine.set_visible(True)
                        spine.set_linewidth(2)
                        spine.set_edgecolor('gray')

        # 更新关节位置时间序列图
        if 'joints' in self.axes and 'qpos' in self.time_series:
            ax = self.axes['joints']
            ax.clear()
            ax.set_title('Joint Positions Over Time', fontsize=12, fontweight='bold')
            ax.set_xlabel('Frame', fontsize=10)
            ax.set_ylabel('Position (rad/m)', fontsize=10)
            ax.grid(True, alpha=0.3)
            ax.set_facecolor('#f8f9fa')

            qpos_data = self.time_series['qpos']
            frames = np.arange(len(qpos_data))

            # 绘制每个关节的时间序列，只显示前面的关节以避免图表过于拥挤
            max_joints_to_show = min(14, qpos_data.shape[1])  # 最多显示14个关节

            for i in range(max_joints_to_show):
                color = self.colors[i % len(self.colors)]
                ax.plot(frames, qpos_data[:, i],
                       label=self.joint_names[i],
                       color=color,
                       linewidth=1.5,
                       alpha=0.8)

            # 添加当前帧的垂直线
            ax.axvline(x=self.current_frame, color='red', linestyle='--',
                      linewidth=2, alpha=0.7, label='Current Frame')

            # 设置图例
            ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=8)

            # 设置x轴范围，显示当前帧周围的数据
            window_size = min(200, len(frames))  # 显示窗口大小
            start_frame = max(0, self.current_frame - window_size // 2)
            end_frame = min(len(frames), start_frame + window_size)
            ax.set_xlim(start_frame, end_frame)

        # 更新动作时间序列图
        if 'actions' in self.axes and 'action' in self.time_series:
            ax = self.axes['actions']
            ax.clear()
            ax.set_title('Actions Over Time', fontsize=12, fontweight='bold')
            ax.set_xlabel('Frame', fontsize=10)
            ax.set_ylabel('Action Value', fontsize=10)
            ax.grid(True, alpha=0.3)
            ax.set_facecolor('#f8f9fa')

            action_data = self.time_series['action']
            frames = np.arange(len(action_data))

            # 绘制每个动作维度的时间序列
            for i in range(action_data.shape[1]):
                color = self.colors[i % len(self.colors)]
                ax.plot(frames, action_data[:, i],
                       label=self.action_names[i],
                       color=color,
                       linewidth=1.5,
                       alpha=0.8)

            # 添加当前帧的垂直线
            ax.axvline(x=self.current_frame, color='red', linestyle='--',
                      linewidth=2, alpha=0.7, label='Current Frame')

            # 设置图例
            ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=8)

            # 设置x轴范围，显示当前帧周围的数据
            window_size = min(200, len(frames))  # 显示窗口大小
            start_frame = max(0, self.current_frame - window_size // 2)
            end_frame = min(len(frames), start_frame + window_size)
            ax.set_xlim(start_frame, end_frame)

        self.fig.canvas.draw_idle()
    
    def show(self):
        """显示可视化界面"""
        plt.tight_layout()
        plt.show()

def main():
    parser = argparse.ArgumentParser(description='Visualize HDF5 robot dataset')
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument('--file', type=str, help='Path to specific HDF5 file')
    group.add_argument('--data_dir', type=str, help='Directory containing HDF5 files')
    
    args = parser.parse_args()
    
    if args.file:
        if not os.path.exists(args.file):
            print(f"Error: File {args.file} not found")
            return
        visualizer = HDF5Visualizer(args.file)
        visualizer.show()
    
    elif args.data_dir:
        if not os.path.exists(args.data_dir):
            print(f"Error: Directory {args.data_dir} not found")
            return
        
        # 列出所有HDF5文件
        hdf5_files = [f for f in os.listdir(args.data_dir) if f.endswith('.hdf5')]
        if not hdf5_files:
            print(f"No HDF5 files found in {args.data_dir}")
            return
        
        hdf5_files.sort()
        print(f"Found {len(hdf5_files)} HDF5 files:")
        for i, f in enumerate(hdf5_files):
            print(f"  {i}: {f}")
        
        # 让用户选择文件
        try:
            choice = input(f"Enter file number (0-{len(hdf5_files)-1}) or press Enter for first file: ")
            if choice.strip() == "":
                choice = 0
            else:
                choice = int(choice)
            
            if 0 <= choice < len(hdf5_files):
                file_path = os.path.join(args.data_dir, hdf5_files[choice])
                visualizer = HDF5Visualizer(file_path)
                visualizer.show()
            else:
                print("Invalid choice")
        except (ValueError, KeyboardInterrupt):
            print("Cancelled")

if __name__ == '__main__':
    main()
