# 2024.10.30
# 将ros1 bag 批量转换为ros2 bag 需修改指定文件夹
import os
import subprocess


def convert_ros1_bag_to_ros2(ros1bag_path, ros2bag_path):
    process = subprocess.Popen(["rosbags-convert", "--src", ros1bag_path, "--dst",ros2bag_path])
    process.wait()

def batch_convert_ros1_bags(input_folder, output_folder):
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)

    for bag_file in os.listdir(input_folder):
        if bag_file.endswith(".bag"):
            ros1bag_path = os.path.join(input_folder, bag_file)
            ros2bag_path = os.path.join(output_folder, bag_file[:-4])
            convert_ros1_bag_to_ros2(ros1bag_path, ros2bag_path)
            print(f"Converted {bag_file}")

if __name__ == "__main__":
    input_folder = "/home/<USER>/dataset1029/record"
    output_folder = input_folder+ "/ros2bags"
    batch_convert_ros1_bags(input_folder, output_folder)
