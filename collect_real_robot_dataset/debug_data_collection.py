#!/usr/bin/env python3
"""
调试数据收集脚本
检查各个数据流的收集情况

Usage:
    python debug_data_collection.py --parent_dir /path/to/ros2bags
"""

import os
import time
import argparse
import threading
import subprocess

import h5py
import numpy as np
import rclpy
from rclpy.node import Node
from sensor_msgs.msg import JointState, Image
from std_msgs.msg import Float64MultiArray, String, Int32MultiArray
from cv_bridge import CvBridge

# Define 14 arm joint names
ARM_JOINTS = [f'Left_Arm_Joint{i}' for i in range(1,8)] + [f'Right_Arm_Joint{i}' for i in range(1,8)]
# Map hand commands to discrete values
HAND_MAP = {'release': 0, 'grasp_high_power': 1}

class DebugRecorder(Node):
    def __init__(self, timeout=10.0):
        super().__init__('debug_recorder')
        self.timeout = timeout
        self.last_msg_time = self.get_clock().now()
        self.close_flag = False
        self.bridge = CvBridge()
        
        # 统计计数器
        self.counters = {
            'images_head': 0,
            'images_left': 0,
            'images_right': 0,
            'joints': 0,
            'left_arm_cmd': 0,
            'right_arm_cmd': 0,
            'left_hand_cmd': 0,
            'right_hand_cmd': 0,
            'left_gripper': 0,
            'right_gripper': 0,
        }
        
        # 最新数据样本
        self.latest_samples = {}
        
        # Subscriptions
        self.create_subscription(Image, '/camera/color/image_raw',          self.cb_head_image,   10)
        self.create_subscription(Image, '/camera1/camera1/color/image_raw', self.cb_left_image,   10)
        self.create_subscription(Image, '/camera2/camera2/color/image_raw', self.cb_right_image,  10)
        self.create_subscription(JointState, '/joint_states',                self.cb_joint_states, 10)
        self.create_subscription(Float64MultiArray,
                                 '/left_arm_position_controller/commands', self.cb_left_arm_cmd,  10)
        self.create_subscription(Float64MultiArray,
                                 '/right_arm_position_controller/commands',self.cb_right_arm_cmd, 10)
        self.create_subscription(String, '/left_hand_command',   self.cb_left_hand_cmd,  10)
        self.create_subscription(String, '/right_hand_command',  self.cb_right_hand_cmd, 10)
        self.create_subscription(Int32MultiArray, '/left_gripper_position',  self.cb_left_gripper,  10)
        self.create_subscription(Int32MultiArray, '/right_gripper_position', self.cb_right_gripper, 10)
        
        # Timer to detect timeout and print stats
        self.create_timer(2.0, self.print_stats)
        self.create_timer(1.0, self.check_timeout)

    def check_timeout(self):
        now = self.get_clock().now()
        if (now.nanoseconds - self.last_msg_time.nanoseconds) * 1e-9 > self.timeout:
            self.get_logger().info('Timeout reached, stopping recording')
            self.close_flag = True

    def print_stats(self):
        """打印统计信息"""
        print(f"\n{'='*60}")
        print(f"Data Collection Statistics (every 2 seconds)")
        print(f"{'='*60}")
        
        for key, count in self.counters.items():
            status = "✓" if count > 0 else "✗"
            print(f"{status} {key:<20}: {count:>6} messages")
            
            # 显示最新样本
            if key in self.latest_samples:
                sample = self.latest_samples[key]
                if isinstance(sample, dict):
                    if 'vals' in sample:
                        print(f"    Latest: {sample['vals'][:5]}..." if len(sample['vals']) > 5 else f"    Latest: {sample['vals']}")
                    elif 'val' in sample:
                        print(f"    Latest: {sample['val']}")
                    elif 'pos' in sample:
                        print(f"    Latest: {sample['pos'][:5]}..." if len(sample['pos']) > 5 else f"    Latest: {sample['pos']}")
        
        print(f"{'='*60}")

    # Image callbacks
    def cb_head_image(self, msg: Image):
        self.counters['images_head'] += 1
        ts = msg.header.stamp.sec * 1e9 + msg.header.stamp.nanosec
        self.latest_samples['images_head'] = {'time': ts, 'shape': f"{msg.height}x{msg.width}"}
        self.last_msg_time = self.get_clock().now()

    def cb_left_image(self, msg: Image):
        self.counters['images_left'] += 1
        ts = msg.header.stamp.sec * 1e9 + msg.header.stamp.nanosec
        self.latest_samples['images_left'] = {'time': ts, 'shape': f"{msg.height}x{msg.width}"}
        self.last_msg_time = self.get_clock().now()

    def cb_right_image(self, msg: Image):
        self.counters['images_right'] += 1
        ts = msg.header.stamp.sec * 1e9 + msg.header.stamp.nanosec
        self.latest_samples['images_right'] = {'time': ts, 'shape': f"{msg.height}x{msg.width}"}
        self.last_msg_time = self.get_clock().now()

    # Joint state callback
    def cb_joint_states(self, msg: JointState):
        self.counters['joints'] += 1
        ts = msg.header.stamp.sec * 1e9 + msg.header.stamp.nanosec
        pos = []
        for name in ARM_JOINTS:
            try: 
                idx = msg.name.index(name)
                pos.append(msg.position[idx])
            except ValueError: 
                pos.append(0.0)
        self.latest_samples['joints'] = {'time': ts, 'pos': pos}
        self.last_msg_time = self.get_clock().now()

    # Action callbacks
    def cb_left_arm_cmd(self, msg: Float64MultiArray):
        self.counters['left_arm_cmd'] += 1
        ts = msg.layout.data_offset or self.get_clock().now().nanoseconds
        self.latest_samples['left_arm_cmd'] = {'time': ts, 'vals': list(msg.data)}
        self.last_msg_time = self.get_clock().now()
        print(f"DEBUG: Left arm command received: {list(msg.data)}")

    def cb_right_arm_cmd(self, msg: Float64MultiArray):
        self.counters['right_arm_cmd'] += 1
        ts = msg.layout.data_offset or self.get_clock().now().nanoseconds
        self.latest_samples['right_arm_cmd'] = {'time': ts, 'vals': list(msg.data)}
        self.last_msg_time = self.get_clock().now()
        print(f"DEBUG: Right arm command received: {list(msg.data)}")

    def cb_left_hand_cmd(self, msg: String):
        self.counters['left_hand_cmd'] += 1
        ts = self.get_clock().now().nanoseconds
        v = HAND_MAP.get(msg.data, 0)
        self.latest_samples['left_hand_cmd'] = {'time': ts, 'val': v}
        self.last_msg_time = self.get_clock().now()
        print(f"DEBUG: Left hand command received: {msg.data} -> {v}")

    def cb_right_hand_cmd(self, msg: String):
        self.counters['right_hand_cmd'] += 1
        ts = self.get_clock().now().nanoseconds
        v = HAND_MAP.get(msg.data, 0)
        self.latest_samples['right_hand_cmd'] = {'time': ts, 'val': v}
        self.last_msg_time = self.get_clock().now()
        print(f"DEBUG: Right hand command received: {msg.data} -> {v}")

    # Gripper state callbacks
    def cb_left_gripper(self, msg: Int32MultiArray):
        self.counters['left_gripper'] += 1
        ts = self.get_clock().now().nanoseconds
        self.latest_samples['left_gripper'] = {'time': ts, 'vals': list(msg.data)}
        self.last_msg_time = self.get_clock().now()

    def cb_right_gripper(self, msg: Int32MultiArray):
        self.counters['right_gripper'] += 1
        ts = self.get_clock().now().nanoseconds
        self.latest_samples['right_gripper'] = {'time': ts, 'vals': list(msg.data)}
        self.last_msg_time = self.get_clock().now()

def play_bag(path):
    time.sleep(2.0)
    subprocess.Popen(['ros2', 'bag', 'play', path])

def check_topics():
    """检查可用的topics"""
    print("Checking available topics...")
    try:
        result = subprocess.run(['ros2', 'topic', 'list'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            topics = result.stdout.strip().split('\n')
            print(f"Found {len(topics)} topics:")
            
            # 检查我们关心的topics
            target_topics = [
                '/camera/color/image_raw',
                '/camera1/camera1/color/image_raw', 
                '/camera2/camera2/color/image_raw',
                '/joint_states',
                '/left_arm_position_controller/commands',
                '/right_arm_position_controller/commands',
                '/left_hand_command',
                '/right_hand_command',
                '/left_gripper_position',
                '/right_gripper_position'
            ]
            
            for topic in target_topics:
                status = "✓" if topic in topics else "✗"
                print(f"  {status} {topic}")
        else:
            print("Failed to list topics")
    except Exception as e:
        print(f"Error checking topics: {e}")

# Main entry
if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('--parent_dir', required=True)
    parser.add_argument('--timeout', type=float, default=15.0)
    args = parser.parse_args()

    bags = sorted([d for d in os.listdir(args.parent_dir)
                   if os.path.isdir(os.path.join(args.parent_dir, d))])
    
    if not bags:
        print(f"No bag directories found in {args.parent_dir}")
        exit(1)

    # 选择第一个bag进行调试
    bag = bags[0]
    bag_dir = os.path.join(args.parent_dir, bag)
    
    if not os.path.exists(os.path.join(bag_dir, 'metadata.yaml')):
        print(f"No metadata.yaml found in {bag_dir}")
        exit(1)
    
    print(f"Debugging bag: {bag}")
    print(f"Timeout: {args.timeout} seconds")
    
    # 检查topics
    check_topics()
    
    # 启动bag播放
    threading.Thread(target=play_bag, args=(bag_dir,), daemon=True).start()
    
    # 启动调试记录器
    rclpy.init()
    recorder = DebugRecorder(timeout=args.timeout)
    
    print(f"\nStarting debug recording for {args.timeout} seconds...")
    print("Watch for action command messages...")
    
    while rclpy.ok() and not recorder.close_flag:
        rclpy.spin_once(recorder)
    
    print(f"\nFinal Statistics:")
    recorder.print_stats()
    
    recorder.destroy_node()
    rclpy.shutdown()
